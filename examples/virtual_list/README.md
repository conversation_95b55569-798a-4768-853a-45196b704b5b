This example showcases Floem's ability to have 1 million fixed height items in a list. 

Behind the scenes, it is keeping track of the list items that are visible and keeping or adding the items that are in view and removing the view items that are out of view.

The Floem `VirtualList` gives you a way to deal with extremely long lists in a performant way without manually doing the adding and removing of views from the view tree.
