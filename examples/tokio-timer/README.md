# Timer

This is an example timer app, as described in
[task 4][task4] of [7gui tasks][7gui].

> Timer deals with concurrency in the sense that a timer process
> that updates the elapsed time runs concurrently to the user’s
> interactions with the GUI application. This also means that the
> solution to competing user and signal interactions is tested. The
> fact that slider adjustments must be reflected immediately moreover
> tests the responsiveness of the solution. A good solution will make
> it clear that the signal is a timer tick and, as always, has not
> much scaffolding.

This examples shows how to integrate tokio streams with a Floem application

![timer](https://github.com/lapce/floem/assets/23398472/b55dae4f-56fe-4e9f-a0ee-1898db048588)

[task4]: https://eugenkiss.github.io/7guis/tasks/#timer
[7gui]: https://eugenkiss.github.io/7guis/
