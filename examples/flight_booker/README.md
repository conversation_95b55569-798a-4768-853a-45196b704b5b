# Flight Booker

This is an example that emulates an application that books flights, as
described in [task 3][task3] of [7gui tasks][7gui].

> The focus of Flight Booker lies on modelling constraints between
> widgets on the one hand and modelling constraints within a widget
> on the other hand. Such constraints are very common in everyday
> interactions with GUI applications. A good solution for Flight Booker
> will make the constraints clear, succinct and explicit in the source
> code and not hidden behind a lot of scaffolding.

| Initial state | Invalid date format | Return date before start date |
| -------       | -------             | -------                       |
| ![valid]      | ![invalid]          | ![return-disabled]            |

[task3]: https://eugenkiss.github.io/7guis/tasks/#flight
[7gui]: https://eugenkiss.github.io/7guis/

[valid]: https://github.com/lapce/floem/assets/23398472/fe2758d3-7161-43a3-b059-8a4a1ce0c02e
[invalid]: https://github.com/lapce/floem/assets/23398472/aeb843aa-520b-48f3-a39d-6acb414dba57
[return-disabled]: https://github.com/lapce/floem/assets/23398472/8f1268f9-efbd-4a4d-9a47-7a50425e3e39