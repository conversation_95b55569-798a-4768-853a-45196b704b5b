[package]
name = "webgpu"
edition = "2021"
license.workspace = true
version.workspace = true

[dependencies]
im.workspace = true
floem = { path = "../.." }
cosmic-text = { version = "0.12.1", features = ["shape-run-cache"] }

[target.'cfg(target_arch = "wasm32")'.dependencies]
console_error_panic_hook = "0.1.6"
console_log = "1.0"
wgpu = { version = "26.0.1" }
wasm-bindgen = "0.2"
wasm-bindgen-futures = "0.4.30"
web-sys = { version = "0.3.77", features = ["Document", "Window", "Element"] }
