//! <PERSON><PERSON>lo Planner
//! Responsabile della pianificazione delle azioni e della gestione dei task agentici.
//! Adattato da Task/TaskState di Cline.
//!
//! ## Integrazione
//! - Interagisce con Orchestrator per la pianificazione dei task
//! - <PERSON><PERSON><PERSON> ricevere input da EventBridge

/// Rappresenta un task pianificato (stub, adattato da Task)
/// Usato dal planner per descrivere un'azione pianificata.
#[derive(Clone)]
pub struct PlannedTask {
    /// Identificativo univoco del task
    pub id: String,
    /// Descrizione sintetica del task
    pub description: String,
    // Altri campi da estendere
}

/// Planner principale
/// Gestisce la pianificazione e la coda dei task.
pub struct Planner {
    queue: Vec<PlannedTask>,
}

impl Planner {
    /// Crea un nuovo planner.
    pub fn new() -> Self {
        Planner {
            queue: Vec::new(),
        }
    }

    /// Pianifica un nuovo task da una descrizione
    /// Crea un PlannedTask dalla descrizione e lo restituisce
    pub fn plan_task(&mut self, description: &str) -> Result<PlannedTask, String> {
        let planned_task = PlannedTask {
            id: uuid::Uuid::new_v4().to_string(),
            description: description.to_string(),
        };

        self.queue.push(planned_task.clone());
        Ok(planned_task)
    }

    /// Recupera e rimuove il prossimo task pianificato dalla coda (se presente)
    pub fn next_task(&mut self) -> Option<PlannedTask> {
        if !self.queue.is_empty() {
            Some(self.queue.remove(0))
        } else {
            None
        }
    }

    /// Restituisce la coda attuale dei task pianificati (solo lettura)
    pub fn get_queue(&self) -> &Vec<PlannedTask> {
        &self.queue
    }
}
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_plan_and_next_task() {
        let mut planner = Planner::new();
        let task = PlannedTask {
            id: "t1".to_string(),
            description: "test".to_string(),
        };
        planner.plan_task(task);
        assert_eq!(planner.get_queue().len(), 1);
        let next = planner.next_task();
        assert!(next.is_some());
        assert_eq!(planner.get_queue().len(), 0);
    }
}