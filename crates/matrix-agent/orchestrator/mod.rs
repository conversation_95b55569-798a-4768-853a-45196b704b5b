//! Modulo Orchestrator
//! Coordina i moduli principali e gestisce il ciclo di vita delle operazioni agentiche.
//! Adattato da Controller/ServiceRegistry di Cline per integrazione completa.

use std::sync::{Arc, RwLock, Mutex};
use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use tokio::sync::mpsc;
use uuid::Uuid;
use async_trait::async_trait;

use crate::event_bridge::{EventBridge, AgentEvent, EventSource, EventTarget};
use crate::planner::{Planner, PlannedTask};
use crate::executor::Executor;
use crate::memory::Memory;
use crate::tools::ToolDefinition;

/// Stato di un task nell'orchestrator
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TaskStatus {
    Pending,
    Running,
    Completed,
    Failed(String),
    Cancelled,
}

/// Task agentico con metadati completi
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AgentTask {
    pub id: Uuid,
    pub description: String,
    pub status: TaskStatus,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub cline_compatible: bool,
    pub context: serde_json::Value,
    pub result: Option<serde_json::Value>,
}

/// Errori dell'orchestrator
#[derive(Debug, thiserror::Error)]
pub enum OrchestratorError {
    #[error("Task not found: {0}")]
    TaskNotFound(Uuid),
    #[error("Event bridge error: {0}")]
    EventBridgeError(String),
    #[error("Execution error: {0}")]
    ExecutionError(String),
    #[error("Planning error: {0}")]
    PlanningError(String),
}

/// Orchestrator principale con integrazione Cline completa
pub struct Orchestrator {
    /// Planner per task decomposition
    planner: Arc<Mutex<Planner>>,
    /// Executor per esecuzione task
    executor: Arc<Executor>,
    /// Memory per context management
    memory: Arc<Mutex<Memory>>,
    /// Event bridge per comunicazione con Cline
    event_bridge: Arc<EventBridge>,
    /// Task attivi
    active_tasks: Arc<RwLock<HashMap<Uuid, AgentTask>>>,
}

impl Orchestrator {
    /// Crea un nuovo orchestrator con integrazione Cline
    pub fn new(
        planner: Arc<Mutex<Planner>>,
        executor: Arc<Executor>,
        memory: Arc<Mutex<Memory>>,
        event_bridge: Arc<EventBridge>,
    ) -> Self {
        Orchestrator {
            planner,
            executor,
            memory,
            event_bridge,
            active_tasks: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Inizializza l'orchestrator e registra handlers per Cline
    pub async fn initialize(&self) -> Result<(), OrchestratorError> {
        // Registra handler per eventi Cline
        let active_tasks = self.active_tasks.clone();
        let event_bridge = self.event_bridge.clone();

        self.event_bridge.register_handler("cline_task_request", Arc::new(move |event| {
            Self::handle_cline_task_request(event, &active_tasks, &event_bridge)
        })).map_err(|e| OrchestratorError::EventBridgeError(e.to_string()))?;

        // Avvia processing eventi
        self.event_bridge.start_processing().await
            .map_err(|e| OrchestratorError::EventBridgeError(e.to_string()))?;

        log::info!("Orchestrator initialized with Cline integration");
        Ok(())
    }

    /// Esegue un task con integrazione Cline completa
    pub async fn execute_task(&self, description: &str, cline_compatible: bool) -> Result<Uuid, OrchestratorError> {
        let task_id = Uuid::new_v4();
        let now = chrono::Utc::now();

        // Crea task
        let task = AgentTask {
            id: task_id,
            description: description.to_string(),
            status: TaskStatus::Pending,
            created_at: now,
            updated_at: now,
            cline_compatible,
            context: serde_json::json!({}),
            result: None,
        };

        // Salva task
        {
            let mut tasks = self.active_tasks.write().map_err(|e| {
                OrchestratorError::ExecutionError(format!("Failed to acquire task lock: {}", e))
            })?;
            tasks.insert(task_id, task.clone());
        }

        // Se compatibile con Cline, invia evento
        if cline_compatible {
            let event = self.event_bridge.create_cline_event(
                "matrix_task_request",
                serde_json::to_value(&task).map_err(|e| {
                    OrchestratorError::ExecutionError(format!("Failed to serialize task: {}", e))
                })?,
                Some(EventTarget::ClineController),
            );

            self.event_bridge.send_event(event).map_err(|e| {
                OrchestratorError::EventBridgeError(e.to_string())
            })?;
        }

        // Avvia esecuzione asincrona
        let planner = self.planner.clone();
        let executor = self.executor.clone();
        let memory = self.memory.clone();
        let active_tasks = self.active_tasks.clone();

        tokio::spawn(async move {
            if let Err(e) = Self::execute_task_internal(task_id, planner, executor, memory, active_tasks).await {
                log::error!("Task execution failed for {}: {}", task_id, e);
            }
        });

        Ok(task_id)
    }

    /// Ottiene lo stato di un task
    pub fn get_task_status(&self, task_id: Uuid) -> Result<TaskStatus, OrchestratorError> {
        let tasks = self.active_tasks.read().map_err(|e| {
            OrchestratorError::ExecutionError(format!("Failed to acquire task lock: {}", e))
        })?;

        let task = tasks.get(&task_id).ok_or(OrchestratorError::TaskNotFound(task_id))?;
        Ok(task.status.clone())
    }

    /// Ottiene tutti i task attivi
    pub fn get_active_tasks(&self) -> Result<Vec<AgentTask>, OrchestratorError> {
        let tasks = self.active_tasks.read().map_err(|e| {
            OrchestratorError::ExecutionError(format!("Failed to acquire task lock: {}", e))
        })?;

        Ok(tasks.values().cloned().collect())
    }

    /// Handler per richieste task da Cline
    fn handle_cline_task_request(
        event: &AgentEvent,
        active_tasks: &Arc<RwLock<HashMap<Uuid, AgentTask>>>,
        event_bridge: &Arc<EventBridge>,
    ) -> Result<(), crate::event_bridge::EventBridgeError> {
        log::info!("Received Cline task request: {}", event.event_type);

        // Processa richiesta Cline e crea task MATRIX corrispondente
        // Implementazione specifica per integrazione Cline

        Ok(())
    }

    /// Esecuzione interna del task
    async fn execute_task_internal(
        task_id: Uuid,
        planner: Arc<Mutex<Planner>>,
        executor: Arc<Executor>,
        memory: Arc<Mutex<Memory>>,
        active_tasks: Arc<RwLock<HashMap<Uuid, AgentTask>>>,
    ) -> Result<(), OrchestratorError> {
        // Aggiorna stato a Running
        Self::update_task_status(task_id, TaskStatus::Running, &active_tasks)?;

        // Pianifica task
        let planned_task = planner.lock().unwrap().plan_task(&format!("Task {}", task_id))
            .map_err(|e| OrchestratorError::PlanningError(e.to_string()))?;

        // Esegui task
        let result = executor.execute(&planned_task.description)
            .map_err(|e| OrchestratorError::ExecutionError(e.to_string()))?;

        // Aggiorna memoria
        memory.lock().unwrap().store_result(&task_id.to_string(), &result);

        // Aggiorna stato a Completed
        Self::update_task_status(task_id, TaskStatus::Completed, &active_tasks)?;

        log::info!("Task {} completed successfully", task_id);
        Ok(())
    }

    /// Aggiorna stato di un task
    fn update_task_status(
        task_id: Uuid,
        status: TaskStatus,
        active_tasks: &Arc<RwLock<HashMap<Uuid, AgentTask>>>,
    ) -> Result<(), OrchestratorError> {
        let mut tasks = active_tasks.write().map_err(|e| {
            OrchestratorError::ExecutionError(format!("Failed to acquire task lock: {}", e))
        })?;

        if let Some(task) = tasks.get_mut(&task_id) {
            task.status = status;
            task.updated_at = chrono::Utc::now();
        }

        Ok(())
    }

    /// Avvia il ciclo di orchestrazione legacy (per compatibilità)
    pub fn start(&mut self) {
        log::warn!("Using legacy start() method. Use initialize() and execute_task() instead.");

        // Mantieni compatibilità con test esistenti
        let task = PlannedTask {
            id: "legacy-1".to_string(),
            description: "Legacy task execution".to_string(),
        };

        if let Ok(planned) = self.planner.lock().unwrap().plan_task(&task.description) {
            if let Ok(result) = self.executor.execute(&planned.description) {
                self.memory.lock().unwrap().store_result(&task.id, &result);
                log::info!("Legacy task completed: {}", result);
            }
        }
    }
}
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_orchestrator_start() {
        let mut orch = Orchestrator::new();
        orch.start();
    }
}