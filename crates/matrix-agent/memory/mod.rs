//! Modulo Memory
//! Gestione della memoria a breve/lungo termine e interfaccia con il memory fabric.
//! Adattato da Memory/TaskState di Cline.

/// Rappresenta una voce di memoria (stub)
pub struct MemoryEntry {
    pub key: String,
    pub value: String,
}

/// Gestore della memoria
pub struct Memory {
    entries: Vec<MemoryEntry>,
}

impl Memory {
    /// Crea un nuovo gestore memoria.
    pub fn new() -> Self {
        Memory {
            entries: Vec::new(),
        }
    }

    /// Salva una voce di memoria (stub API)
    pub fn save(&mut self, entry: MemoryEntry) {
        // Sostituisce se esiste già una voce con la stessa chiave
        if let Some(existing) = self.entries.iter_mut().find(|e| e.key == entry.key) {
            existing.value = entry.value;
        } else {
            self.entries.push(entry);
        }
    }

    /// Recupera una voce di memoria (stub API)
    pub fn get(&self, key: &str) -> Option<MemoryEntry> {
        self.entries.iter()
            .find(|e| e.key == key)
            .map(|e| MemoryEntry {
                key: e.key.clone(),
                value: e.value.clone(),
            })
    }

    /// Memorizza il risultato di un task
    pub fn store_result(&mut self, task_id: &str, result: &str) {
        let entry = MemoryEntry {
            key: format!("task_result_{}", task_id),
            value: result.to_string(),
        };
        self.save(entry);
    }
}
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_save_and_get_memory() {
        let mut memory = Memory::new();
        let entry = MemoryEntry {
            key: "k".to_string(),
            value: "v".to_string(),
        };
        memory.save(entry);
        let res = memory.get("k");
        assert!(res.is_some());
        assert_eq!(res.unwrap().value, "v");
    }
}