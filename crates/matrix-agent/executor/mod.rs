//! Modulo Executor
//! Responsabile dell'esecuzione delle azioni pianificate dal planner.
//! Adattato da ToolExecutor di Cline.

use crate::tools::ToolDefinition;

/// Executor principale
pub struct Executor;

impl Executor {
    /// Crea un nuovo executor.
    pub fn new() -> Self {
        Executor
    }

    /// Esegue uno strumento su input (stub API)
    pub fn execute_tool(&self, tool: &ToolDefinition, input: &str) -> Result<String, String> {
        let output = (tool.run)(input);
        Ok(output)
    }

    /// Esegue un task dalla sua descrizione
    pub fn execute(&self, description: &str) -> Result<String, String> {
        // Implementazione semplificata per MVP
        // In futuro, questo dovrebbe analizzare la descrizione e scegliere gli strumenti appropriati
        Ok(format!("Executed task: {}", description))
    }
}
#[cfg(test)]
mod tests {
    use super::*;
    use crate::tools::ToolDefinition;

    fn dummy_tool() -> ToolDefinition {
        ToolDefinition {
            name: "dummy".to_string(),
            description: "test".to_string(),
            run: |input| format!("ok:{}", input),
        }
    }

    #[test]
    fn test_execute_tool() {
        let exec = Executor::new();
        let tool = dummy_tool();
        let res = exec.execute_tool(&tool, "ciao").unwrap();
        assert_eq!(res, "ok:ciao");
    }
}