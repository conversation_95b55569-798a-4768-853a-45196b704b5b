//! Modulo per la gestione delle impostazioni
//!
//! Fornisce funzionalità per la gestione delle impostazioni utente e di workspace,
//! inclusa la lettura/scrittura da/su file e l'applicazione di override.

use crate::error::{DataError, DataResult};
use crate::DataConfig;
use serde::{Serialize, Deserialize};
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};

/// Rappresenta le impostazioni di MATRIX IDE
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Settings {
    /// Impostazioni dell'editor
    #[serde(default)]
    pub editor: EditorSettings,

    /// Impostazioni dell'interfaccia utente
    #[serde(default)]
    pub ui: UiSettings,

    /// Impostazioni delle funzionalità
    #[serde(default)]
    pub features: FeatureSettings,

    /// Impostazioni dei plugin
    #[serde(default)]
    pub plugins: HashMap<String, toml::Value>,

    /// Impostazioni avanzate
    #[serde(default)]
    pub advanced: AdvancedSettings,
}

impl Settings {
    /// Crea nuove impostazioni con i valori predefiniti
    pub fn new() -> Self {
        Self::default()
    }

    /// Unisce queste impostazioni con un'altra istanza, dando priorità ai valori dell'altra
    pub fn merge(&self, other: &Settings) -> Settings {
        let mut result = self.clone();

        // Unisci le impostazioni dell'editor
        result.editor = self.editor.merge(&other.editor);

        // Unisci le impostazioni dell'interfaccia utente
        result.ui = self.ui.merge(&other.ui);

        // Unisci le impostazioni delle funzionalità
        result.features = self.features.merge(&other.features);

        // Unisci le impostazioni avanzate
        result.advanced = self.advanced.merge(&other.advanced);

        // Unisci le impostazioni dei plugin
        for (key, value) in &other.plugins {
            result.plugins.insert(key.clone(), value.clone());
        }

        result
    }
}

impl Default for Settings {
    fn default() -> Self {
        Self {
            editor: EditorSettings::default(),
            ui: UiSettings::default(),
            features: FeatureSettings::default(),
            plugins: HashMap::new(),
            advanced: AdvancedSettings::default(),
        }
    }
}

/// Impostazioni dell'editor
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EditorSettings {
    /// Dimensione del font
    #[serde(default = "default_font_size")]
    pub font_size: u32,

    /// Nome del font
    #[serde(default = "default_font_family")]
    pub font_family: String,

    /// Visualizza i numeri di linea
    #[serde(default = "default_true")]
    pub line_numbers: bool,

    /// Evidenzia la linea corrente
    #[serde(default = "default_true")]
    pub highlight_current_line: bool,

    /// Lunghezza tab (in spazi)
    #[serde(default = "default_tab_size")]
    pub tab_size: u32,

    /// Usa spazi invece di tab
    #[serde(default = "default_true")]
    pub use_spaces: bool,

    /// Wrapping automatico delle linee
    #[serde(default = "default_false")]
    pub word_wrap: bool,

    /// Mostra caratteri invisibili
    #[serde(default = "default_false")]
    pub show_invisibles: bool,

    /// Abilita l'auto-salvataggio
    #[serde(default = "default_true")]
    pub auto_save: bool,

    /// Intervallo di auto-salvataggio (in secondi)
    #[serde(default = "default_auto_save_interval")]
    pub auto_save_interval: u32,

    /// Impostazioni aggiuntive dell'editor
    #[serde(default)]
    pub additional: HashMap<String, toml::Value>,
}

impl EditorSettings {
    /// Unisce queste impostazioni con un'altra istanza
    pub fn merge(&self, other: &EditorSettings) -> EditorSettings {
        let mut result = self.clone();

        // Sostituisci i valori non predefiniti dall'altra istanza
        if other.font_size != default_font_size() {
            result.font_size = other.font_size;
        }

        if other.font_family != default_font_family() {
            result.font_family = other.font_family.clone();
        }

        if other.line_numbers != default_true() {
            result.line_numbers = other.line_numbers;
        }

        if other.highlight_current_line != default_true() {
            result.highlight_current_line = other.highlight_current_line;
        }

        if other.tab_size != default_tab_size() {
            result.tab_size = other.tab_size;
        }

        if other.use_spaces != default_true() {
            result.use_spaces = other.use_spaces;
        }

        if other.word_wrap != default_false() {
            result.word_wrap = other.word_wrap;
        }

        if other.show_invisibles != default_false() {
            result.show_invisibles = other.show_invisibles;
        }

        if other.auto_save != default_true() {
            result.auto_save = other.auto_save;
        }

        if other.auto_save_interval != default_auto_save_interval() {
            result.auto_save_interval = other.auto_save_interval;
        }

        // Unisci le impostazioni aggiuntive
        for (key, value) in &other.additional {
            result.additional.insert(key.clone(), value.clone());
        }

        result
    }
}

impl Default for EditorSettings {
    fn default() -> Self {
        Self {
            font_size: default_font_size(),
            font_family: default_font_family(),
            line_numbers: default_true(),
            highlight_current_line: default_true(),
            tab_size: default_tab_size(),
            use_spaces: default_true(),
            word_wrap: default_false(),
            show_invisibles: default_false(),
            auto_save: default_true(),
            auto_save_interval: default_auto_save_interval(),
            additional: HashMap::new(),
        }
    }
}

/// Impostazioni dell'interfaccia utente
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiSettings {
    /// Tema dell'interfaccia
    #[serde(default = "default_theme")]
    pub theme: String,

    /// Mostra la sidebar all'avvio
    #[serde(default = "default_true")]
    pub show_sidebar: bool,

    /// Posizione della sidebar (left o right)
    #[serde(default = "default_sidebar_position")]
    pub sidebar_position: String,

    /// Larghezza della sidebar (in pixel)
    #[serde(default = "default_sidebar_width")]
    pub sidebar_width: u32,

    /// Mostra la minimap
    #[serde(default = "default_true")]
    pub show_minimap: bool,

    /// Mostra la barra di stato
    #[serde(default = "default_true")]
    pub show_statusbar: bool,

    /// Mostra il pannello dei problemi
    #[serde(default = "default_true")]
    pub show_problems_panel: bool,

    /// Livello di zoom dell'interfaccia
    #[serde(default = "default_zoom_level")]
    pub zoom_level: i32,

    /// Impostazioni aggiuntive dell'interfaccia
    #[serde(default)]
    pub additional: HashMap<String, toml::Value>,
}

impl UiSettings {
    /// Unisce queste impostazioni con un'altra istanza
    pub fn merge(&self, other: &UiSettings) -> UiSettings {
        let mut result = self.clone();

        // Sostituisci i valori non predefiniti dall'altra istanza
        if other.theme != default_theme() {
            result.theme = other.theme.clone();
        }

        if other.show_sidebar != default_true() {
            result.show_sidebar = other.show_sidebar;
        }

        if other.sidebar_position != default_sidebar_position() {
            result.sidebar_position = other.sidebar_position.clone();
        }

        if other.sidebar_width != default_sidebar_width() {
            result.sidebar_width = other.sidebar_width;
        }

        if other.show_minimap != default_true() {
            result.show_minimap = other.show_minimap;
        }

        if other.show_statusbar != default_true() {
            result.show_statusbar = other.show_statusbar;
        }

        if other.show_problems_panel != default_true() {
            result.show_problems_panel = other.show_problems_panel;
        }

        if other.zoom_level != default_zoom_level() {
            result.zoom_level = other.zoom_level;
        }

        // Unisci le impostazioni aggiuntive
        for (key, value) in &other.additional {
            result.additional.insert(key.clone(), value.clone());
        }

        result
    }
}

impl Default for UiSettings {
    fn default() -> Self {
        Self {
            theme: default_theme(),
            show_sidebar: default_true(),
            sidebar_position: default_sidebar_position(),
            sidebar_width: default_sidebar_width(),
            show_minimap: default_true(),
            show_statusbar: default_true(),
            show_problems_panel: default_true(),
            zoom_level: default_zoom_level(),
            additional: HashMap::new(),
        }
    }
}

/// Impostazioni delle funzionalità
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureSettings {
    /// Abilita il completamento automatico
    #[serde(default = "default_true")]
    pub auto_complete: bool,

    /// Abilita il controllo della sintassi
    #[serde(default = "default_true")]
    pub syntax_checking: bool,

    /// Abilita il controllo ortografico
    #[serde(default = "default_false")]
    pub spell_checking: bool,

    /// Abilita i suggerimenti dell'IA
    #[serde(default = "default_true")]
    pub ai_suggestions: bool,

    /// Abilita il debugging integrato
    #[serde(default = "default_true")]
    pub integrated_debugging: bool,

    /// Abilita il terminale integrato
    #[serde(default = "default_true")]
    pub integrated_terminal: bool,

    /// Abilita l'integrazione con git
    #[serde(default = "default_true")]
    pub git_integration: bool,

    /// Impostazioni aggiuntive delle funzionalità
    #[serde(default)]
    pub additional: HashMap<String, toml::Value>,
}

impl FeatureSettings {
    /// Unisce queste impostazioni con un'altra istanza
    pub fn merge(&self, other: &FeatureSettings) -> FeatureSettings {
        let mut result = self.clone();

        // Sostituisci i valori non predefiniti dall'altra istanza
        if other.auto_complete != default_true() {
            result.auto_complete = other.auto_complete;
        }

        if other.syntax_checking != default_true() {
            result.syntax_checking = other.syntax_checking;
        }

        if other.spell_checking != default_false() {
            result.spell_checking = other.spell_checking;
        }

        if other.ai_suggestions != default_true() {
            result.ai_suggestions = other.ai_suggestions;
        }

        if other.integrated_debugging != default_true() {
            result.integrated_debugging = other.integrated_debugging;
        }

        if other.integrated_terminal != default_true() {
            result.integrated_terminal = other.integrated_terminal;
        }

        if other.git_integration != default_true() {
            result.git_integration = other.git_integration;
        }

        // Unisci le impostazioni aggiuntive
        for (key, value) in &other.additional {
            result.additional.insert(key.clone(), value.clone());
        }

        result
    }
}

impl Default for FeatureSettings {
    fn default() -> Self {
        Self {
            auto_complete: default_true(),
            syntax_checking: default_true(),
            spell_checking: default_false(),
            ai_suggestions: default_true(),
            integrated_debugging: default_true(),
            integrated_terminal: default_true(),
            git_integration: default_true(),
            additional: HashMap::new(),
        }
    }
}

/// Impostazioni avanzate
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdvancedSettings {
    /// Numero massimo di file recenti
    #[serde(default = "default_max_recent_files")]
    pub max_recent_files: u32,

    /// Dimensione massima della cache (in MB)
    #[serde(default = "default_max_cache_size")]
    pub max_cache_size: u32,

    /// Abilita la registrazione estesa
    #[serde(default = "default_false")]
    pub extended_logging: bool,

    /// Livello di dettaglio della registrazione
    #[serde(default = "default_log_level")]
    pub log_level: String,

    /// Numero massimo di thread per le operazioni di background
    #[serde(default = "default_max_threads")]
    pub max_threads: u32,
    
    /// Abilita funzionalità sperimentali
    #[serde(default = "default_false")]
    pub experimental_features: bool,
    
    /// Percorso personalizzato per le estensioni
    #[serde(default)]
    pub custom_extensions_path: Option<String>,
    
    /// Disabilita la telemetria
    #[serde(default = "default_false")]
    pub disable_telemetry: bool,
    
    /// Percorso personalizzato per la configurazione
    #[serde(default)]
    pub custom_config_path: Option<String>,

    /// Impostazioni aggiuntive avanzate
    #[serde(default)]
    pub additional: HashMap<String, toml::Value>,
}

impl AdvancedSettings {
    /// Unisce queste impostazioni con un'altra istanza
    pub fn merge(&self, other: &AdvancedSettings) -> AdvancedSettings {
        let mut result = self.clone();

        // Sostituisci i valori non predefiniti dall'altra istanza
        if other.max_recent_files != default_max_recent_files() {
            result.max_recent_files = other.max_recent_files;
        }

        if other.max_cache_size != default_max_cache_size() {
            result.max_cache_size = other.max_cache_size;
        }

        if other.extended_logging != default_false() {
            result.extended_logging = other.extended_logging;
        }

        if other.log_level != default_log_level() {
            result.log_level = other.log_level.clone();
        }

        if other.max_threads != default_max_threads() {
            result.max_threads = other.max_threads;
        }
        
        if other.experimental_features != default_false() {
            result.experimental_features = other.experimental_features;
        }
        
        if other.custom_extensions_path != None {
            result.custom_extensions_path = other.custom_extensions_path.clone();
        }
        
        if other.disable_telemetry != default_false() {
            result.disable_telemetry = other.disable_telemetry;
        }
        
        if other.custom_config_path != None {
            result.custom_config_path = other.custom_config_path.clone();
        }

        // Unisci le impostazioni aggiuntive
        for (key, value) in &other.additional {
            result.additional.insert(key.clone(), value.clone());
        }

        result
    }
}
impl Default for AdvancedSettings {
    fn default() -> Self {
        Self {
            max_recent_files: default_max_recent_files(),
            max_cache_size: default_max_cache_size(),
            extended_logging: default_false(),
            log_level: default_log_level(),
            max_threads: default_max_threads(),
            experimental_features: default_false(),
            custom_extensions_path: None,
            disable_telemetry: default_false(),
            custom_config_path: None,
            additional: HashMap::new(),
        }
    }
}

// Funzioni per valori predefiniti

fn default_true() -> bool { true }
fn default_false() -> bool { false }
fn default_font_size() -> u32 { 14 }
fn default_font_family() -> String { "JetBrains Mono".to_string() }
fn default_tab_size() -> u32 { 4 }
fn default_auto_save_interval() -> u32 { 60 }
fn default_theme() -> String { "dark".to_string() }
fn default_sidebar_position() -> String { "left".to_string() }
fn default_sidebar_width() -> u32 { 250 }
fn default_zoom_level() -> i32 { 0 }
fn default_max_recent_files() -> u32 { 30 }
fn default_max_cache_size() -> u32 { 500 }
fn default_log_level() -> String { "info".to_string() }
fn default_max_threads() -> u32 { 4 }

/// Impostazioni specifiche dell'utente
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct UserSettings {
    /// Impostazioni di base
    #[serde(flatten)]
    pub base: Settings,

    /// Data dell'ultimo aggiornamento
    #[serde(default = "chrono::Utc::now")]
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// Impostazioni specifiche del workspace
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct WorkspaceSettings {
    /// Impostazioni di base
    #[serde(flatten)]
    pub base: Settings,

    /// Data dell'ultimo aggiornamento
    #[serde(default = "chrono::Utc::now")]
    pub last_updated: chrono::DateTime<chrono::Utc>,

    /// Percorsi da escludere nel workspace
    #[serde(default)]
    pub exclude_patterns: Vec<String>,
}
/// Gestore delle impostazioni
pub struct SettingsManager {
    config: DataConfig,
    user_settings: Arc<RwLock<UserSettings>>,
    workspace_settings: Arc<RwLock<Option<WorkspaceSettings>>>,
}

impl SettingsManager {
    /// Crea un nuovo gestore delle impostazioni
    pub fn new(config: &DataConfig) -> DataResult<Self> {
        // Carica le impostazioni utente
        let user_settings = Self::load_user_settings(config)?;

        Ok(Self {
            config: config.clone(),
            user_settings: Arc::new(RwLock::new(user_settings)),
            workspace_settings: Arc::new(RwLock::new(None)),
        })
    }

    /// Carica le impostazioni utente
    fn load_user_settings(config: &DataConfig) -> DataResult<UserSettings> {
        let settings_path = Self::get_user_settings_path(config);

        if settings_path.exists() {
            let content = std::fs::read_to_string(&settings_path)
                .map_err(|e| DataError::IoError(format!("Impossibile leggere le impostazioni utente: {}", e)))?;

            let settings: UserSettings = toml::from_str(&content)
                .map_err(|e| DataError::SerializationError(format!("Impossibile deserializzare le impostazioni utente: {}", e)))?;

            Ok(settings)
        } else {
            // Crea impostazioni predefinite
            let settings = UserSettings {
                base: Settings::default(),
                last_updated: chrono::Utc::now(),
            };

            // Crea la directory dei settings se non esiste
            if let Some(parent) = settings_path.parent() {
                std::fs::create_dir_all(parent)
                    .map_err(|e| DataError::IoError(format!("Impossibile creare la directory delle impostazioni: {}", e)))?;
            }

            // Salva le impostazioni predefinite
            let content = toml::to_string_pretty(&settings)
                .map_err(|e| DataError::SerializationError(format!("Impossibile serializzare le impostazioni utente: {}", e)))?;

            std::fs::write(&settings_path, content)
                .map_err(|e| DataError::IoError(format!("Impossibile scrivere le impostazioni utente: {}", e)))?;

            Ok(settings)
        }
    }

    /// Carica le impostazioni del workspace
    pub fn load_workspace_settings(&self, workspace_path: &Path) -> DataResult<()> {
        let settings_path = workspace_path.join(".matrix").join("settings.toml");

        let workspace_settings = if settings_path.exists() {
            let content = std::fs::read_to_string(&settings_path)
                .map_err(|e| DataError::IoError(format!("Impossibile leggere le impostazioni del workspace: {}", e)))?;

            let settings: WorkspaceSettings = toml::from_str(&content)
                .map_err(|e| DataError::SerializationError(format!("Impossibile deserializzare le impostazioni del workspace: {}", e)))?;

            Some(settings)
        } else {
            None
        };

        let mut ws_settings = self.workspace_settings.write()
            .map_err(|_| DataError::LockError("Impossibile acquisire il lock per le impostazioni del workspace".to_string()))?;

        *ws_settings = workspace_settings;

        Ok(())
    }

    /// Salva le impostazioni utente
    pub fn save_user_settings(&self) -> DataResult<()> {
        let settings_path = Self::get_user_settings_path(&self.config);

        let settings = self.user_settings.read()
            .map_err(|_| DataError::LockError("Impossibile acquisire il lock per le impostazioni utente".to_string()))?;

        let content = toml::to_string_pretty(&*settings)
            .map_err(|e| DataError::SerializationError(format!("Impossibile serializzare le impostazioni utente: {}", e)))?;

        std::fs::write(&settings_path, content)
            .map_err(|e| DataError::IoError(format!("Impossibile scrivere le impostazioni utente: {}", e)))?;

        Ok(())
    }

    /// Salva le impostazioni del workspace
    pub fn save_workspace_settings(&self, workspace_path: &Path) -> DataResult<()> {
        let ws_settings = self.workspace_settings.read()
            .map_err(|_| DataError::LockError("Impossibile acquisire il lock per le impostazioni del workspace".to_string()))?;

        if let Some(settings) = &*ws_settings {
            let settings_dir = workspace_path.join(".matrix");
            std::fs::create_dir_all(&settings_dir)
                .map_err(|e| DataError::IoError(format!("Impossibile creare la directory delle impostazioni: {}", e)))?;

            let settings_path = settings_dir.join("settings.toml");

            let content = toml::to_string_pretty(settings)
                .map_err(|e| DataError::SerializationError(format!("Impossibile serializzare le impostazioni del workspace: {}", e)))?;

            std::fs::write(&settings_path, content)
                .map_err(|e| DataError::IoError(format!("Impossibile scrivere le impostazioni del workspace: {}", e)))?;
        }

        Ok(())
    }

    /// Ottiene il percorso delle impostazioni utente
    fn get_user_settings_path(config: &DataConfig) -> PathBuf {
        config.base_dir.join("settings").join("user.toml")
    }

    /// Ottiene le impostazioni combinate (utente + workspace)
    pub fn get_combined_settings(&self) -> DataResult<Settings> {
        let user_settings = self.user_settings.read()
            .map_err(|_| DataError::LockError("Impossibile acquisire il lock per le impostazioni utente".to_string()))?;

        let ws_settings = self.workspace_settings.read()
            .map_err(|_| DataError::LockError("Impossibile acquisire il lock per le impostazioni del workspace".to_string()))?;

        let mut combined = user_settings.base.clone();

        if let Some(ws) = &*ws_settings {
            combined = combined.merge(&ws.base);
        }

        Ok(combined)
    }

    /// Aggiorna le impostazioni utente
    pub fn update_user_settings(&self, settings: Settings) -> DataResult<()> {
        let mut user_settings = self.user_settings.write()
            .map_err(|_| DataError::LockError("Impossibile acquisire il lock per le impostazioni utente".to_string()))?;

        user_settings.base = settings;
        user_settings.last_updated = chrono::Utc::now();

        self.save_user_settings()?;

        Ok(())
    }

    /// Aggiorna le impostazioni del workspace
    pub fn update_workspace_settings(&self, settings: Settings, workspace_path: &Path) -> DataResult<()> {
        let mut ws_settings = self.workspace_settings.write()
            .map_err(|_| DataError::LockError("Impossibile acquisire il lock per le impostazioni del workspace".to_string()))?;

        let workspace_settings = WorkspaceSettings {
            base: settings,
            last_updated: chrono::Utc::now(),
            exclude_patterns: if let Some(ws) = &*ws_settings {
                ws.exclude_patterns.clone()
            } else {
                Vec::new()
            },
        };

        *ws_settings = Some(workspace_settings);

        self.save_workspace_settings(workspace_path)?;

        Ok(())
    }
}