[package]
name = "matrix-data"
version.workspace = true
edition = "2021"
authors.workspace = true
license.workspace = true
repository.workspace = true
description = "Data layer for MATRIX IDE, handling persistence, settings, and project structure"

[dependencies]
# Dipendenze dal workspace
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true
thiserror.workspace = true
log.workspace = true
env_logger.workspace = true
async-trait.workspace = true

# Dipendenze interne
matrix-core = { path = "../matrix-core" }

# Dipendenze specifiche per data
dirs = "5.0"
toml = "0.7"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.2", features = ["v4", "serde"] }
walkdir = "2.3"
rusqlite = { version = "0.29", features = ["bundled"] }
dashmap = "5.5"
glob = "0.3"
