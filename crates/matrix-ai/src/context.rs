//! Modulo per la gestione del contesto per l'IA
//!
//! Fornisce funzionalità per estrarre, gestire e organizzare il contesto
//! da fornire ai modelli linguistici.

use crate::error::AiResult;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// Rappresenta un blocco di contesto di codice
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CodeBlock {
    /// Contenuto del blocco di codice
    pub content: String,

    /// Linguaggio del codice
    pub language: String,

    /// Path del file, se disponibile
    pub file_path: Option<String>,

    /// Range di linee nel file originale (inizio, fine)
    pub line_range: Option<(usize, usize)>,

    /// Peso/importanza di questo blocco (0.0-1.0)
    pub relevance: f32,
}

/// Rappresenta un frammento di contesto testuale
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TextBlock {
    /// Contenuto del blocco di testo
    pub content: String,

    /// Tipo di testo (documentazione, commento, prompt, ecc.)
    pub block_type: String,

    /// Peso/importanza di questo blocco (0.0-1.0)
    pub relevance: f32,
}

/// Tipo di contesto disponibile
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ContextBlock {
    /// Blocco di codice
    Code(CodeBlock),

    /// Blocco di testo
    Text(TextBlock),
}

/// Rappresenta un contesto completo per un'operazione di AI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeContext {
    /// Blocchi di contesto, ordinati per rilevanza
    pub blocks: Vec<ContextBlock>,

    /// Metadati aggiuntivi del contesto
    pub metadata: HashMap<String, String>,
}
/// Estrae i blocchi di contesto da un singolo file.
///
/// # Parametri
/// - `file_path`: percorso del file da cui estrarre il contesto.
/// 
/// # Ritorna
/// Un vettore di blocchi di contesto estratti dal file.
///
/// # Errori
/// Restituisce un errore se il file non può essere letto o processato.
pub async fn extract_from_file(file_path: &str) -> AiResult<Vec<ContextBlock>> {
    use tokio::fs;
    use tokio::io::AsyncReadExt;

    let mut file = fs::File::open(file_path).await.map_err(|e| crate::error::AiError::msg(e.to_string()))?;
    let mut content = String::new();
    file.read_to_string(&mut content).await.map_err(|e| crate::error::AiError::msg(e.to_string()))?;

    // Determina il linguaggio dal path (estensione)
    let language = file_path.split('.').last().unwrap_or("").to_string();

    let code_block = CodeBlock {
        content: content.clone(),
        language,
        file_path: Some(file_path.to_string()),
        line_range: None,
        relevance: 1.0,
    };

    Ok(vec![ContextBlock::Code(code_block)])
}

/// Estrae i blocchi di contesto da una lista di file.
///
/// # Parametri
/// - `file_paths`: lista dei percorsi dei file da cui estrarre il contesto.
/// 
/// # Ritorna
/// Un vettore di blocchi di contesto estratti da tutti i file.
///
/// # Errori
/// Restituisce un errore se uno dei file non può essere letto o processato.
pub async fn extract_from_files(file_paths: &[String]) -> AiResult<Vec<ContextBlock>> {
    let mut blocks = Vec::new();
    for path in file_paths {
        let mut file_blocks = extract_from_file(path).await?;
        // Rilevanza decrescente per i file successivi
        let rel = if blocks.is_empty() { 1.0 } else { 0.8 };
        for block in &mut file_blocks {
            match block {
                ContextBlock::Code(code) => code.relevance = rel,
                ContextBlock::Text(text) => text.relevance = rel,
            }
        }
        blocks.extend(file_blocks);
    }
    Ok(blocks)
}

/// Ordina i blocchi di contesto per rilevanza decrescente.
///
/// # Parametri
/// - `blocks`: vettore di blocchi di contesto da ordinare.
/// 
/// # Ritorna
/// Un nuovo vettore di blocchi ordinati per rilevanza.
pub fn sort_by_relevance(blocks: &[ContextBlock]) -> Vec<ContextBlock> {
    let mut sorted = blocks.to_vec();
    sorted.sort_by(|a, b| {
        let rel_a = match a {
            ContextBlock::Code(code) => code.relevance,
            ContextBlock::Text(text) => text.relevance,
        };
        let rel_b = match b {
            ContextBlock::Code(code) => code.relevance,
            ContextBlock::Text(text) => text.relevance,
        };
        rel_b.partial_cmp(&rel_a).unwrap_or(std::cmp::Ordering::Equal)
    });
    sorted
}

/// Serializza un `CodeContext` in formato JSON.
///
/// # Parametri
/// - `context`: riferimento al contesto da serializzare.
/// 
/// # Ritorna
/// Una stringa JSON rappresentante il contesto.
///
/// # Errori
/// Restituisce un errore se la serializzazione fallisce.
pub fn serialize_context(context: &CodeContext) -> AiResult<String> {
    serde_json::to_string(context).map_err(|e| crate::error::AiError::msg(e.to_string()))
}

impl CodeContext {
    /// Crea un nuovo contesto vuoto
    pub fn new() -> Self {
        Self {
            blocks: Vec::new(),
            metadata: HashMap::new(),
        }
    }

    /// Aggiunge un blocco di codice al contesto
    pub fn add_code_block(&mut self, block: CodeBlock) {
        self.blocks.push(ContextBlock::Code(block));
    }

    /// Aggiunge un blocco di testo al contesto
    pub fn add_text_block(&mut self, block: TextBlock) {
        self.blocks.push(ContextBlock::Text(block));
    }

    /// Aggiunge un metadato al contesto
    pub fn add_metadata(&mut self, key: impl Into<String>, value: impl Into<String>) {
        self.metadata.insert(key.into(), value.into());
    }

    /// Ordina i blocchi per rilevanza
    pub fn sort_by_relevance(&mut self) {
        self.blocks.sort_by(|a, b| {
            let relevance_a = match a {
                ContextBlock::Code(code) => code.relevance,
                ContextBlock::Text(text) => text.relevance,
            };

            let relevance_b = match b {
                ContextBlock::Code(code) => code.relevance,
                ContextBlock::Text(text) => text.relevance,
            };

            relevance_b.partial_cmp(&relevance_a).unwrap_or(std::cmp::Ordering::Equal)
        });
    }

    /// Stima il numero di token utilizzati da questo contesto
    pub fn estimate_tokens(&self) -> usize {
        // Stima approssimativa: 4 caratteri per token
        let mut total_chars = 0;

        for block in &self.blocks {
            match block {
                ContextBlock::Code(code) => total_chars += code.content.len(),
                ContextBlock::Text(text) => total_chars += text.content.len(),
            }
        }

        // Aggiungi caratteri dai metadati
        for (key, value) in &self.metadata {
            total_chars += key.len() + value.len();
        }

        // Approssimazione: 1 token ogni 4 caratteri
        total_chars / 4
    }
}

impl Default for CodeContext {
    fn default() -> Self {
        Self::new()
    }
}

/// Gestore del contesto per operazioni AI
pub struct ContextManager {
    /// Numero massimo di token consentiti nel contesto
    max_tokens: usize,
}

impl ContextManager {
    /// Crea un nuovo gestore del contesto
    pub fn new(max_tokens: usize) -> Self {
        Self { max_tokens }
    }

    /// Estrae il contesto da un file di codice
    pub fn extract_from_file(&self, file_content: &str, file_path: &str, language: &str) -> AiResult<CodeContext> {
        let mut context = CodeContext::new();

        // Estrai il codice completo come un singolo blocco
        // In un'implementazione reale, questo analizzerebbe la struttura del codice
        // e lo suddividerebbe in blocchi logici (funzioni, classi, ecc.)
        let code_block = CodeBlock {
            content: file_content.to_string(),
            language: language.to_string(),
            file_path: Some(file_path.to_string()),
            line_range: None, // Tutto il file
            relevance: 1.0,   // Massima rilevanza
        };

        context.add_code_block(code_block);
        context.add_metadata("source_file", file_path);
        context.add_metadata("language", language);

        Ok(context)
    }

    /// Estrae il contesto da più file, considerando la rilevanza
    pub fn extract_from_files(
        &self,
        files: Vec<(String, String, String)>, // (content, path, language)
        focus_path: Option<&str>,
    ) -> AiResult<CodeContext> {
        let mut context = CodeContext::new();

        for (content, path, language) in files {
            // Assegna rilevanza più alta al file di focus
            let relevance = if let Some(focus) = focus_path {
                if path == focus { 1.0 } else { 0.7 }
            } else {
                0.8 // Rilevanza di default
            };

            let code_block = CodeBlock {
                content,
                language,
                file_path: Some(path.clone()),
                line_range: None,
                relevance,
            };

            context.add_code_block(code_block);
            context.add_metadata(format!("file_{}", context.blocks.len()), path);
        }

        // Ordina i blocchi per rilevanza
        context.sort_by_relevance();

        Ok(context)
    }

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs::File;
    use std::io::Write;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_extract_from_file() {
        let dir = tempdir().unwrap();
        let file_path = dir.path().join("test.rs");
        let mut file = File::create(&file_path).unwrap();
        writeln!(file, "fn main() {{ println!(\"Hello\"); }}").unwrap();

        let blocks = extract_from_file(file_path.to_str().unwrap()).await.unwrap();
        assert_eq!(blocks.len(), 1);
        match &blocks[0] {
            ContextBlock::Code(code) => {
                assert!(code.content.contains("fn main()"));
                assert_eq!(code.language, "rs");
                assert_eq!(code.relevance, 1.0);
            }
            _ => panic!("Expected CodeBlock"),
        }
    }

    #[tokio::test]
    async fn test_extract_from_files() {
        let dir = tempdir().unwrap();
        let file1 = dir.path().join("a.rs");
        let file2 = dir.path().join("b.rs");
        File::create(&file1).unwrap().write_all(b"fn a() {}").unwrap();
        File::create(&file2).unwrap().write_all(b"fn b() {}").unwrap();

        let files = vec![
            file1.to_str().unwrap().to_string(),
            file2.to_str().unwrap().to_string(),
        ];
        let blocks = extract_from_files(&files).await.unwrap();
        assert_eq!(blocks.len(), 2);
        assert!(blocks.iter().any(|b| match b {
            ContextBlock::Code(code) => code.content.contains("fn a()"),
            _ => false,
        }));
        assert!(blocks.iter().any(|b| match b {
            ContextBlock::Code(code) => code.content.contains("fn b()"),
            _ => false,
        }));
    }

    #[test]
    fn test_sort_by_relevance() {
        let block1 = ContextBlock::Code(CodeBlock {
            content: "a".into(),
            language: "rs".into(),
            file_path: None,
            line_range: None,
            relevance: 0.5,
        });
        let block2 = ContextBlock::Text(TextBlock {
            content: "b".into(),
            block_type: "doc".into(),
            relevance: 0.9,
        });
        let sorted = sort_by_relevance(&[block1.clone(), block2.clone()]);
        assert_eq!(sorted[0], block2);
        assert_eq!(sorted[1], block1);
    }

    #[test]
    fn test_serialize_context() {
        let mut ctx = CodeContext::new();
        ctx.add_code_block(CodeBlock {
            content: "code".into(),
            language: "rs".into(),
            file_path: Some("main.rs".into()),
            line_range: Some((1, 2)),
            relevance: 1.0,
        });
        ctx.add_text_block(TextBlock {
            content: "doc".into(),
            block_type: "doc".into(),
            relevance: 0.5,
        });
        let json = serialize_context(&ctx).unwrap();
        assert!(json.contains("main.rs"));
        assert!(json.contains("doc"));
    }

    /// Ottimizza il contesto per adattarlo al limite di token
    pub fn optimize_context(&self, context: &mut CodeContext) -> AiResult<()> {
        // Stima i token correnti
        let current_tokens = context.estimate_tokens();

        // Se siamo sotto il limite, non serve ottimizzare
        if current_tokens <= self.max_tokens {
            return Ok(());
        }

        // Ordina per rilevanza (importante farsi prima, in caso di rimozione)
        context.sort_by_relevance();

        // Calcola di quanto dobbiamo ridurre
        let reduction_factor = self.max_tokens as f32 / current_tokens as f32;

        // Riduci i blocchi meno rilevanti
        let mut blocks = std::mem::take(&mut context.blocks);
        let mut current_size = 0;
        let mut new_blocks = Vec::new();

        for block in blocks {
            let block_size = match &block {
                ContextBlock::Code(code) => code.content.len(),
                ContextBlock::Text(text) => text.content.len(),
            } / 4; // Stima token

            // Se aggiungendo questo blocco superiamo il limite, lo saltiamo
            if current_size + block_size > self.max_tokens {
                continue;
            }

            new_blocks.push(block);
            current_size += block_size;
        }

        context.blocks = new_blocks;

        Ok(())
    }
}
