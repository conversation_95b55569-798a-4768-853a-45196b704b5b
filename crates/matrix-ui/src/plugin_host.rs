//! Gestore dei plugin per l'interfaccia utente
//!
//! Questo modulo implementa il PluginHost che gestisce il ciclo di vita dei plugin
//! e l'integrazione delle loro viste nell'interfaccia utente.

use std::sync::{Arc, RwLock};
use std::collections::{HashMap, HashSet};

use matrix_core::{
    Engine as CoreEngine,
    Plugin, 
    PluginView, 
    PluginPanelType,
    Event, 
    EventBus, 
    EventHandler, 
    EventSubscriber,
    GlobalState,
    CoreError,
    LogLevel,
};

use crate::{
    error::UiError,
    panels::{Panel, PanelType, PanelManager},
    theme::ThemeManager,
};

use floem::{
    views::View,
    reactive::RwSignal,
};

use async_trait::async_trait;
use uuid::Uuid;
use serde::{Serialize, Deserialize};
use serde_json::json;

/// Informazioni su una vista di plugin
#[derive(<PERSON><PERSON>, Debug)]
pub struct PluginViewInfo {
    /// ID del plugin
    pub plugin_id: String,
    /// ID della vista
    pub view_id: String,
    /// ID del pannello associato
    pub panel_id: String,
    /// Tipo di pannello
    pub panel_type: PanelType,
    /// Titolo della vista
    pub title: String,
    /// Se la vista è visibile
    pub visible: bool,
}

/// Errore del gestore dei plugin
#[derive(Debug, thiserror::Error)]
pub enum PluginHostError {
    #[error("Errore del core: {0}")]
    CoreError(#[from] CoreError),

    #[error("Errore dell'interfaccia utente: {0}")]
    UiError(#[from] UiError),

    #[error("Plugin non trovato: {0}")]
    PluginNotFound(String),

    #[error("Vista non trovata: {0}")]
    ViewNotFound(String),

    #[error("Errore di lock: {0}")]
    LockError(String),

    #[error("Operazione non supportata: {0}")]
    UnsupportedOperation(String),

    #[error("Errore generico: {0}")]
    Generic(String),
}


/// Stato di un plugin
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PluginState {
    /// Plugin non inizializzato
    Uninitialized,
    /// Plugin inizializzato
    Initialized,
    /// Plugin in esecuzione
    Running,
    /// Plugin in errore
    Error,
}

/// Informazioni su un plugin caricato
#[derive(Debug, Clone)]
pub struct LoadedPlugin {
    /// ID del plugin
    pub id: String,
    /// Nome del plugin
    pub name: String,
    /// Stato del plugin
    pub state: PluginState,
    /// ID della sottoscrizione agli eventi
    pub subscription_id: Option<Uuid>,
    /// Set delle viste del plugin
    pub views: HashSet<String>,
}

/// Tipo di risultato per le operazioni del PluginHost
pub type PluginHostResult<T> = Result<T, PluginHostError>;

/// Gestore dei plugin per l'interfaccia utente
pub struct PluginHost {
    /// Riferimento al core engine
    core: Arc<CoreEngine>,
    /// Gestore dei pannelli
    panel_manager: Arc<PanelManager>,
    /// Gestore dei temi
    theme_manager: Arc<ThemeManager>,
    /// Plugin caricati
    plugins: RwLock<HashMap<String, LoadedPlugin>>,
    /// Viste dei plugin
    views: RwLock<HashMap<String, PluginViewInfo>>,
    /// ID della sottoscrizione agli eventi
    subscription_id: RwLock<Option<Uuid>>,
}

impl PluginHost {
    /// Crea un nuovo gestore dei plugin
    pub fn new(
        core: Arc<CoreEngine>,
        panel_manager: Arc<PanelManager>,
        theme_manager: Arc<ThemeManager>,
    ) -> Self {
        Self {
            core,
            panel_manager,
            theme_manager,
            plugins: RwLock::new(HashMap::new()),
            views: RwLock::new(HashMap::new()),
            subscription_id: RwLock::new(None),
        }
    }

    /// Inizializza il gestore dei plugin
    pub fn initialize(&self) -> PluginHostResult<()> {
        log::info!("Inizializzazione del PluginHost");

        // Sottoscrive agli eventi di plugin
        let event_handler = Arc::new(PluginHostEventHandler {
            plugin_host: Arc::new(self.clone()),
        });

        let subscriber = Arc::new(EventSubscriber::new(
            vec![
                "plugin.loaded", 
                "plugin.unloaded",
                "plugin.view.created",
                "plugin.view.destroyed",
                "plugin.command",
            ],
            event_handler,
        ));

        let subscription_id = self.core.event_bus().subscribe(subscriber)?;

        *self.subscription_id.write().map_err(|_| {
            PluginHostError::LockError("Failed to acquire write lock on subscription ID".to_string())
        })? = Some(subscription_id);

        // Emette un evento di log per indicare l'inizializzazione
        self.emit_log(
            LogLevel::Info,
            "PluginHost inizializzato".to_string(),
            None
        )?;

        Ok(())
    }

    /// Arresta il gestore dei plugin
    pub fn shutdown(&self) -> PluginHostResult<()> {
        log::info!("Arresto del PluginHost");

        // Emette un evento di log per indicare l'arresto
        self.emit_log(
            LogLevel::Info,
            "PluginHost in arresto".to_string(),
            None
        )?;

        // Cancella la sottoscrizione agli eventi
        if let Some(id) = *self.subscription_id.read().map_err(|_| {
            PluginHostError::LockError("Failed to acquire read lock on subscription ID".to_string())
        })? {
            self.core.event_bus().unsubscribe(id)?;
        }

        // Rimuove tutte le viste dei plugin
        let view_ids = {
            let views = self.views.read().map_err(|_| {
                PluginHostError::LockError("Failed to acquire read lock on views".to_string())
            })?;

            views.keys().cloned().collect::<Vec<_>>()
        };

        for view_id in view_ids {
            if let Err(e) = self.remove_plugin_view(&view_id) {
                log::error!("Errore nella rimozione della vista {}: {}", view_id, e);
            }
        }

        // Chiude tutti i plugin
        let plugin_ids = {
            let plugins = self.plugins.read().map_err(|_| {
                PluginHostError::LockError("Failed to acquire read lock on plugins".to_string())
            })?;

            plugins.keys().cloned().collect::<Vec<_>>()
        };

        for plugin_id in plugin_ids {
            if let Err(e) = self.unload_plugin(&plugin_id) {
                log::error!("Errore nello scaricamento del plugin {}: {}", plugin_id, e);
            }
        }

        Ok(())
    }

    /// Carica un plugin
    pub fn load_plugin(&self, plugin: Arc<dyn Plugin>) -> PluginHostResult<()> {
        let info = plugin.info();
        let plugin_id = info.id.clone();

        log::info!("Caricamento del plugin: {}", plugin_id);

        // Verifica che il plugin non sia già caricato
        {
            let plugins = self.plugins.read().map_err(|_| {
                PluginHostError::LockError("Failed to acquire read lock on plugins".to_string())
            })?;

            if plugins.contains_key(&plugin_id) {
                return Err(PluginHostError::Generic(format!("Plugin con ID {} già caricato", plugin_id)));
            }
        }

        // Inizializza il plugin attraverso il core
        let plugin_manager = self.core.plugin_manager();
        plugin_manager.register_plugin(plugin)?;

        // Registra il plugin nel PluginHost
        {
            let mut plugins = self.plugins.write().map_err(|_| {
                PluginHostError::LockError("Failed to acquire write lock on plugins".to_string())
            })?;

            plugins.insert(plugin_id.clone(), LoadedPlugin {
                id: plugin_id.clone(),
                name: info.name.clone(),
                state: PluginState::Initialized,
                subscription_id: None,
                views: HashSet::new(),
            });
        }

        // Emette un log
        self.emit_log(
            LogLevel::Info,
            format!("Plugin caricato: {} ({})", info.name, plugin_id),
            Some(json!({
                "version": info.version,
                "author": info.author,
            }))
        )?;

        Ok(())
    }

    /// Scarica un plugin
    pub fn unload_plugin(&self, plugin_id: &str) -> PluginHostResult<()> {
        log::info!("Scaricamento del plugin: {}", plugin_id);

        // Verifica che il plugin sia caricato
        let plugin_info = {
            let plugins = self.plugins.read().map_err(|_| {
                PluginHostError::LockError("Failed to acquire read lock on plugins".to_string())
            })?;

            match plugins.get(plugin_id) {
                Some(plugin) => plugin.clone(),
                None => return Err(PluginHostError::PluginNotFound(plugin_id.to_string())),
            }
        };

        // Rimuove tutte le viste del plugin
        let view_ids = {
            let mut plugins = self.plugins.write().map_err(|_| {
                PluginHostError::LockError("Failed to acquire write lock on plugins".to_string())
            })?;

            if let Some(plugin) = plugins.get_mut(plugin_id) {
                plugin.views.iter().cloned().collect::<Vec<_>>()
            } else {
                Vec::new()
            }
        };

        for view_id in view_ids {
            if let Err(e) = self.remove_plugin_view(&view_id) {
                log::error!("Errore nella rimozione della vista {}: {}", view_id, e);
            }
        }

        // Scarica il plugin attraverso il core
        let plugin_manager = self.core.plugin_manager();
        plugin_manager.unload_plugin(plugin_id)?;

        // Rimuove il plugin dal PluginHost
        {
            let mut plugins = self.plugins.write().map_err(|_| {
                PluginHostError::LockError("Failed to acquire write lock on plugins".to_string())
            })?;

            plugins.remove(plugin_id);
        }

        // Emette un log
        self.emit_log(
            LogLevel::Info,
            format!("Plugin scaricato: {} ({})", plugin_info.name, plugin_id),
            None
        )?;

        Ok(())
    }

    /// Registra una vista di plugin
    pub fn register_plugin_view(&self, plugin_id: &str) -> PluginHostResult<()> {
        log::info!("Registrazione della vista del plugin: {}", plugin_id);

        // Verifica che il plugin sia caricato
        {
            let plugins = self.plugins.read().map_err(|_| {
                PluginHostError::LockError("Failed to acquire read lock on plugins".to_string())
            })?;

            if !plugins.contains_key(plugin_id) {
                return Err(PluginHostError::PluginNotFound(plugin_id.to_string()));
            }
        }

        // Ottiene la vista dal plugin
        let plugin_manager = self.core.plugin_manager();
        let view_opt = plugin_manager.get_plugin_view(plugin_id)?;

        if let Some(view) = view_opt {
            // Ottiene le informazioni sulla vista
            let view_id = view.get_id();
            let title = view.get_title();
            let panel_type = match view.get_panel_type() {
                PluginPanelType::Left => PanelType::Left,
                PluginPanelType::Right => PanelType::Right,
                PluginPanelType::Bottom => PanelType::Bottom,
                PluginPanelType::Float => PanelType::Float,
            };

            // Crea un pannello per la vista
            let panel_id = format!("plugin-{}-{}", plugin_id, view_id);

            // Crea un'adattatore per la vista
            let view_adapter = move || {
                let floem_view = view.create_floem_view();
                floem_view
            };

            // Crea un pannello
            let panel = Panel::new(
                panel_type,
                &panel_id,
                &title,
                view_adapter,
            );

            // Registra il pannello nel PanelManager
            self.panel_manager.register_panel(panel)?;

            // Registra la vista
            {
                let mut views = self.views.write().map_err(|_| {
                    PluginHostError::LockError("Failed to acquire write lock on views".to_string())
                })?;

                views.insert(view_id.clone(), PluginViewInfo {
                    plugin_id: plugin_id.to_string(),
                    view_id: view_id.clone(),
                    panel_id: panel_id.clone(),
                    panel_type,
                    title,
                    visible: true,
                });
            }

            // Aggiunge la vista al plugin
            {
                let mut plugins = self.plugins.write().map_err(|_| {
                    PluginHostError::LockError("Failed to acquire write lock on plugins".to_string())
                })?;

                if let Some(plugin) = plugins.get_mut(plugin_id) {
                    plugin.views.insert(view_id.clone());
                }
            }

            // Emette un evento di creazione della vista
            self.core.event_bus().emit(Event::PluginViewCreated {
                plugin_id: plugin_id.to_string(),
                view_id: view_id.clone(),
                panel_type: format!("{:?}", panel_type),
            })?;

            // Emette un log
            self.emit_log(
                LogLevel::Info,
                format!("Vista plugin registrata: {} ({})", view_id, plugin_id),
                Some(json!({
                    "panel_id": panel_id,
                    "panel_type": format!("{:?}", panel_type),
                    "title": title,
                }))
            )?;
        }

        Ok(())
    }

    /// Rimuove una vista di plugin
    pub fn remove_plugin_view(&self, view_id: &str) -> PluginHostResult<()> {
        log::info!("Rimozione della vista plugin: {}", view_id);

        // Ottiene le informazioni sulla vista
        let view_info = {
            let views = self.views.read().map_err(|_| {
                PluginHostError::LockError("Failed to acquire read lock on views".to_string())
            })?;

            match views.get(view_id) {
                Some(info) => info.clone(),
                None => return Err(PluginHostError::ViewNotFound(view_id.to_string())),
            }
        };

        // Rimuove la vista dal plugin
        {
            let mut plugins = self.plugins.write().map_err(|_| {
                PluginHostError::LockError("Failed to acquire write lock on plugins".to_string())
            })?;

            if let Some(plugin) = plugins.get_mut(&view_info.plugin_id) {
                plugin.views.remove(view_id);
            }
        }

        // Rimuove il pannello dal PanelManager
        if let Err(e) = self.panel_manager.hide_panel(&view_info.panel_id) {
            log::warn!("Errore nel nascondere il pannello {}: {}", view_info.panel_id, e);
        }

        // Rimuove la vista
        {
            let mut views = self.views.write().map_err(|_| {
                PluginHostError::LockError("Failed to acquire write lock on views".to_string())
            })?;

            views.remove(view_id);
        }

        // Emette un evento di distruzione della vista
        self.core.event_bus().emit(Event::PluginViewDestroyed {
            plugin_id: view_info.plugin_id.clone(),
            view_id: view_id.to_string(),
        })?;

        // Emette un log
        self.emit_log(
            LogLevel::Info,
            format!("Vista plugin rimossa: {} ({})", view_id, view_info.plugin_id),
            None
        )?;

        Ok(())
    }

    /// Mostra una vista di plugin
    pub fn show_plugin_view(&self, view_id: &str) -> PluginHostResult<()> {
        log::info!("Visualizzazione della vista plugin: {}", view_id);

        // Ottiene le informazioni sulla vista
        let view_info = {
            let views = self.views.read().map_err(|_| {
                PluginHostError::LockError("Failed to acquire read lock on views".to_string())
            })?;

            match views.get(view_id) {
                Some(info) => info.clone(),
                None => return Err(PluginHostError::ViewNotFound(view_id.to_string())),
            }
        };

        // Mostra il pannello
        self.panel_manager.show_panel(&view_info.panel_id)?;

        // Aggiorna lo stato della vista
        {
            let mut views = self.views.write().map_err(|_| {
                PluginHostError::LockError("Failed to acquire write lock on views".to_string())
            })?;

            if let Some(info) = views.get_mut(view_id) {
                info.visible = true;
            }
        }

        // Emette un evento di visualizzazione della vista
        self.core.event_bus().emit(Event::PluginViewShown {
            plugin_id: view_info.plugin_id.clone(),
            view_id: view_id.to_string(),
        })?;

        Ok(())
    }

    /// Nasconde una vista di plugin
    pub fn hide_plugin_view(&self, view_id: &str) -> PluginHostResult<()> {
        log::info!("Nascondimento della vista plugin: {}", view_id);

        // Ottiene le informazioni sulla vista
        let view_info = {
            let views = self.views.read().map_err(|_| {
                PluginHostError::LockError("Failed to acquire read lock on views".to_string())
            })?;

            match views.get(view_id) {
                Some(info) => info.clone(),
                None => return Err(PluginHostError::ViewNotFound(view_id.to_string())),
            }
        };

        // Nasconde il pannello
        self.panel_manager.hide_panel(&view_info.panel_id)?;

        // Aggiorna lo stato della vista
        {
            let mut views = self.views.write().map_err(|_| {
                PluginHostError::LockError("Failed to acquire write lock on views".to_string())
            })?;

            if let Some(info) = views.get_mut(view_id) {
                info.visible = false;
            }
        }

        // Emette un evento di nascondimento della vista
        self.core.event_bus().emit(Event::PluginViewHidden {
            plugin_id: view_info.plugin_id.clone(),
            view_id: view_id.to_string(),
        })?;

        Ok(())
    }

    /// Ottiene lo stato di un plugin
    pub fn get_plugin_state(&self, plugin_id: &str) -> PluginHostResult<PluginState> {
        let plugins = self.plugins.read().map_err(|_| {
            PluginHostError::LockError("Failed to acquire read lock on plugins".to_string())
        })?;

        match plugins.get(plugin_id) {
            Some(plugin) => Ok(plugin.state),
            None => Err(PluginHostError::PluginNotFound(plugin_id.to_string())),
        }
    }

    /// Ottiene la lista dei plugin caricati
    pub fn get_loaded_plugins(&self) -> PluginHostResult<Vec<String>> {
        let plugins = self.plugins.read().map_err(|_| {
            PluginHostError::LockError("Failed to acquire read lock on plugins".to_string())
        })?;

        Ok(plugins.keys().cloned().collect())
    }

    /// Ottiene la lista delle viste di un plugin
    pub fn get_plugin_views(&self, plugin_id: &str) -> PluginHostResult<Vec<String>> {
        let plugins = self.plugins.read().map_err(|_| {
            PluginHostError::LockError("Failed to acquire read lock on plugins".to_string())
        })?;

        match plugins.get(plugin_id) {
            Some(plugin) => Ok(plugin.views.iter().cloned().collect()),
            None => Err(PluginHostError::PluginNotFound(plugin_id.to_string())),
        }
    }

    /// Emette un evento di log
    fn emit_log(&self, level: LogLevel, message: String, context: Option<serde_json::Value>) -> PluginHostResult<()> {
        self.core.event_bus().emit(Event::AgentLog {
            level,
            message,
            context,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        })?;

        Ok(())
    }

    /// Ottiene la lista delle viste di un plugin
    pub fn get_plugin_views(&self, plugin_id: &str) -> PluginHostResult<Vec<String>> {
        let plugins = self.plugins.read().map_err(|_| {
            PluginHostError::LockError("Failed to acquire read lock on plugins".to_string())
        })?;

        match plugins.get(plugin_id) {
            Some(plugin) => Ok(plugin.views.iter().cloned().collect()),
            None => Err(PluginHostError::PluginNotFound(plugin_id.to_string())),
        }
    }

    /// Ottiene la lista dei plugin caricati
    pub fn get_loaded_plugins(&self) -> PluginHostResult<Vec<String>> {
        let plugins = self.plugins.read().map_err(|_| {
            PluginHostError::LockError("Failed to acquire read lock on plugins".to_string())
        })?;

        Ok(plugins.keys().cloned().collect())
    }

    /// Ottiene lo stato di un plugin
    pub fn get_plugin_state(&self, plugin_id: &str) -> PluginHostResult<PluginState> {
        let plugins = self.plugins.read().map_err(|_| {
            PluginHostError::LockError("Failed to acquire read lock on plugins".to_string())
        })?;

        match plugins.get(plugin_id) {
            Some(plugin) => Ok(plugin.state),
            None => Err(PluginHostError::PluginNotFound(plugin_id.to_string())),
        }
    }
}

impl Clone for PluginHost {
    fn clone(&self) -> Self {
        Self {
            core: self.core.clone(),
            panel_manager: self.panel_manager.clone(),
            theme_manager: self.theme_manager.clone(),
            plugins: RwLock::new(HashMap::new()),
            views: RwLock::new(HashMap::new()),
            subscription_id: RwLock::new(*self.subscription_id.read().unwrap_or(&None)),
        }
    }
}

/// Gestore degli eventi per il PluginHost
struct PluginHostEventHandler {
    /// Riferimento al PluginHost
    plugin_host: Arc<PluginHost>,
}

#[async_trait]
impl EventHandler for PluginHostEventHandler {
    async fn handle(&self, event: Event) -> Result<(), CoreError> {
        match event {
            Event::PluginLoaded { id, name } => {
                // Un plugin è stato caricato, registriamo le sue viste
                if let Err(e) = self.plugin_host.register_plugin_view(&id) {
                    log::error!("Errore nella registrazione della vista per il plugin {}: {}", id, e);
                }
            }
            Event::PluginUnloaded { id } => {
                // Un plugin è stato scaricato, verifichiamo che sia stato rimosso dal PluginHost
                let plugins = match self.plugin_host.plugins.read() {
                    Ok(plugins) => plugins,
                    Err(e) => {
                        log::error!("Errore nell'accesso ai plugin: {}", e);
                        return Ok(());
                    }
                };

                if plugins.contains_key(&id) {
                    log::warn!("Plugin {} ancora presente nel PluginHost dopo lo scaricamento", id);
                }
            }
            Event::PluginCommand { plugin_id, command, args } => {
                // Elabora un comando specifico di un plugin
                log::debug!("Comando plugin ricevuto: {} da {}", command, plugin_id);

                // In una implementazione reale, qui gestiremo comandi specifici
                // Ad esempio, mostrare/nascondere viste, ricaricare plugin, ecc.
                if command == "show_view" {
                    if let Some(view_id) = args.get("view_id").and_then(|v| v.as_str()) {
                        if let Err(e) = self.plugin_host.show_plugin_view(view_id) {
                            log::error!("Errore nella visualizzazione della vista {}: {}", view_id, e);
                        }
                    }
                } else if command == "hide_view" {
                    if let Some(view_id) = args.get("view_id").and_then(|v| v.as_str()) {
                        if let Err(e) = self.plugin_host.hide_plugin_view(view_id) {
                            log::error!("Errore nel nascondere la vista {}: {}", view_id, e);
                        }
                    }
                }
            }
            _ => {}
        }

        Ok(())
    }
}