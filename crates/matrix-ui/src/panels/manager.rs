//! Panel Manager per MATRIX IDE
//!
//! Questo modulo implementa il sistema di gestione dei pannelli dell'interfaccia utente,
//! fornendo funzionalità di registrazione, layout e docking dei pannelli.

use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use serde::{Serialize, Deserialize};
use floem::View;
use crate::theme::ThemeManager;
use crate::error::UiError;

/// Identificatore univoco per un pannello
pub type PanelId = String;

/// Posizione di un pannello nel layout
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PanelPosition {
    /// Pannello a sinistra
    Left,
    /// Pannello a destra  
    Right,
    /// Pannello in alto
    Top,
    /// Pannello in basso
    Bottom,
    /// Pannello centrale
    Center,
    /// Pannello flottante
    Floating { x: f64, y: f64 },
}

/// Stato di un pannello
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum PanelState {
    /// Pannello visibile
    Visible,
    /// Pannello nascosto
    Hidden,
    /// Pannello minimizzato
    Minimized,
    /// Pannello massimizzato
    Maximized,
}

/// Configurazione di un pannello
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PanelConfig {
    /// ID del pannello
    pub id: PanelId,
    /// Titolo del pannello
    pub title: String,
    /// Posizione nel layout
    pub position: PanelPosition,
    /// Stato corrente
    pub state: PanelState,
    /// Dimensioni (larghezza, altezza)
    pub size: (f64, f64),
    /// Se il pannello è ridimensionabile
    pub resizable: bool,
    /// Se il pannello può essere spostato
    pub movable: bool,
    /// Se il pannello può essere chiuso
    pub closable: bool,
}

/// Trait per i pannelli dell'interfaccia utente
pub trait Panel: Send + Sync {
    /// Restituisce l'ID del pannello
    fn id(&self) -> &str;
    
    /// Restituisce il titolo del pannello
    fn title(&self) -> &str;
    
    /// Crea la vista del pannello
    fn create_view(&self) -> Box<dyn View>;
    
    /// Gestisce l'aggiornamento del pannello
    fn update(&mut self) -> Result<(), UiError>;
    
    /// Gestisce la chiusura del pannello
    fn on_close(&mut self) -> Result<(), UiError> {
        Ok(())
    }
    
    /// Gestisce il ridimensionamento del pannello
    fn on_resize(&mut self, width: f64, height: f64) -> Result<(), UiError> {
        let _ = (width, height);
        Ok(())
    }
}

/// Layout dei pannelli
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PanelLayout {
    /// Configurazioni dei pannelli
    pub panels: HashMap<PanelId, PanelConfig>,
    /// Layout principale (split orizzontale/verticale)
    pub main_split: SplitDirection,
    /// Proporzioni delle aree
    pub split_ratios: Vec<f64>,
}

/// Direzione di split del layout
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SplitDirection {
    /// Split orizzontale (pannelli uno sopra l'altro)
    Horizontal,
    /// Split verticale (pannelli uno accanto all'altro)
    Vertical,
}

/// Gestore dei pannelli dell'interfaccia utente
pub struct PanelManager {
    /// Pannelli registrati
    panels: RwLock<HashMap<PanelId, Box<dyn Panel>>>,
    /// Layout corrente
    layout: RwLock<PanelLayout>,
    /// Gestore dei temi
    theme_manager: Arc<ThemeManager>,
    /// Configurazione salvata
    config_path: Option<std::path::PathBuf>,
}

impl PanelManager {
    /// Crea un nuovo gestore dei pannelli
    pub fn new(theme_manager: Arc<ThemeManager>) -> Self {
        let default_layout = PanelLayout {
            panels: HashMap::new(),
            main_split: SplitDirection::Vertical,
            split_ratios: vec![0.2, 0.6, 0.2], // Left, Center, Right
        };
        
        Self {
            panels: RwLock::new(HashMap::new()),
            layout: RwLock::new(default_layout),
            theme_manager,
            config_path: None,
        }
    }
    
    /// Crea un nuovo gestore con configurazione personalizzata
    pub fn with_config<P: Into<std::path::PathBuf>>(
        theme_manager: Arc<ThemeManager>,
        config_path: P,
    ) -> Result<Self, UiError> {
        let mut manager = Self::new(theme_manager);
        manager.config_path = Some(config_path.into());
        manager.load_config()?;
        Ok(manager)
    }
    
    /// Registra un nuovo pannello
    pub fn register_panel(&self, panel: Box<dyn Panel>) -> Result<(), UiError> {
        let panel_id = panel.id().to_string();
        
        // Aggiungi il pannello alla collezione
        {
            let mut panels = self.panels.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on panels".to_string())
            })?;
            
            if panels.contains_key(&panel_id) {
                return Err(UiError::PanelError(format!("Panel {} already registered", panel_id)));
            }
            
            panels.insert(panel_id.clone(), panel);
        }
        
        // Aggiungi la configurazione di default al layout
        {
            let mut layout = self.layout.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on layout".to_string())
            })?;
            
            let config = PanelConfig {
                id: panel_id.clone(),
                title: panel_id.clone(),
                position: PanelPosition::Center,
                state: PanelState::Visible,
                size: (300.0, 200.0),
                resizable: true,
                movable: true,
                closable: true,
            };
            
            layout.panels.insert(panel_id, config);
        }
        
        Ok(())
    }
    
    /// Rimuove un pannello
    pub fn unregister_panel(&self, panel_id: &str) -> Result<(), UiError> {
        // Rimuovi il pannello
        {
            let mut panels = self.panels.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on panels".to_string())
            })?;
            
            panels.remove(panel_id);
        }
        
        // Rimuovi la configurazione
        {
            let mut layout = self.layout.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on layout".to_string())
            })?;
            
            layout.panels.remove(panel_id);
        }
        
        Ok(())
    }
    
    /// Mostra un pannello
    pub fn show_panel(&self, panel_id: &str) -> Result<(), UiError> {
        let mut layout = self.layout.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on layout".to_string())
        })?;
        
        if let Some(config) = layout.panels.get_mut(panel_id) {
            config.state = PanelState::Visible;
            Ok(())
        } else {
            Err(UiError::PanelError(format!("Panel {} not found", panel_id)))
        }
    }
    
    /// Nasconde un pannello
    pub fn hide_panel(&self, panel_id: &str) -> Result<(), UiError> {
        let mut layout = self.layout.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on layout".to_string())
        })?;
        
        if let Some(config) = layout.panels.get_mut(panel_id) {
            config.state = PanelState::Hidden;
            Ok(())
        } else {
            Err(UiError::PanelError(format!("Panel {} not found", panel_id)))
        }
    }
    
    /// Ottiene la configurazione di un pannello
    pub fn get_panel_config(&self, panel_id: &str) -> Result<PanelConfig, UiError> {
        let layout = self.layout.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on layout".to_string())
        })?;
        
        layout.panels.get(panel_id)
            .cloned()
            .ok_or_else(|| UiError::PanelError(format!("Panel {} not found", panel_id)))
    }
    
    /// Aggiorna la configurazione di un pannello
    pub fn update_panel_config(&self, config: PanelConfig) -> Result<(), UiError> {
        let mut layout = self.layout.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on layout".to_string())
        })?;
        
        layout.panels.insert(config.id.clone(), config);
        Ok(())
    }
    
    /// Ottiene la lista dei pannelli registrati
    pub fn get_panel_ids(&self) -> Result<Vec<String>, UiError> {
        let panels = self.panels.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on panels".to_string())
        })?;
        
        Ok(panels.keys().cloned().collect())
    }
    
    /// Salva la configurazione corrente
    pub fn save_config(&self) -> Result<(), UiError> {
        if let Some(config_path) = &self.config_path {
            let layout = self.layout.read().map_err(|_| {
                UiError::LockError("Failed to acquire read lock on layout".to_string())
            })?;
            
            let config_data = serde_json::to_string_pretty(&*layout)
                .map_err(|e| UiError::SerializationError(e.to_string()))?;
            
            std::fs::write(config_path, config_data)
                .map_err(|e| UiError::IoError(e.to_string()))?;
        }
        
        Ok(())
    }
    
    /// Carica la configurazione salvata
    pub fn load_config(&self) -> Result<(), UiError> {
        if let Some(config_path) = &self.config_path {
            if config_path.exists() {
                let config_data = std::fs::read_to_string(config_path)
                    .map_err(|e| UiError::IoError(e.to_string()))?;
                
                let layout: PanelLayout = serde_json::from_str(&config_data)
                    .map_err(|e| UiError::SerializationError(e.to_string()))?;
                
                *self.layout.write().map_err(|_| {
                    UiError::LockError("Failed to acquire write lock on layout".to_string())
                })? = layout;
            }
        }
        
        Ok(())
    }
}

impl Default for PanelLayout {
    fn default() -> Self {
        Self {
            panels: HashMap::new(),
            main_split: SplitDirection::Vertical,
            split_ratios: vec![0.2, 0.6, 0.2],
        }
    }
}
