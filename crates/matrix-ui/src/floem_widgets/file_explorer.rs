//! Widget dell'esploratore di file
//!
//! Questo modulo fornisce un widget per navigare nella struttura dei file del progetto.

use std::sync::Arc;
use std::path::PathBuf;

use floem::reactive::{RwSignal, create_rw_signal, create_memo, SignalGet, SignalUpdate};
use floem::views::*;
use floem::View;
use floem::style::*;
use floem::peniko::Color;
use floem::event::{Event, EventListener};
use floem::keyboard::{Key, KeyEvent, Modifiers};

use matrix_core::{Engine as CoreEngine, EventBus};
use crate::theme::ThemeManager;

/// Evento quando un file è selezionato nell'esploratore
pub struct FileSelectedEvent {
    /// Percorso del file selezionato
    pub path: PathBuf,
}

/// Stato dell'esploratore di file
pub struct FileExplorerState {
    /// Percorso corrente
    current_path: RwSignal<PathBuf>,

    /// Elementi nella directory corrente
    items: RwSignal<Vec<FileItem>>,

    /// Elemento selezionato
    selected: RwSignal<Option<usize>>,

    /// Flag di ricaricamento
    should_reload: RwSignal<bool>,
}

/// Elemento nell'esploratore di file
#[derive(Debug, Clone)]
pub struct FileItem {
    /// Nome dell'elemento
    pub name: String,

    /// Percorso dell'elemento
    pub path: PathBuf,

    /// È una directory?
    pub is_dir: bool,

    /// È espanso? (solo per directory)
    pub expanded: bool,

    /// Profondità nell'albero
    pub depth: usize,

    /// Elementi figli (solo per directory)
    pub children: Vec<FileItem>,
}

/// Widget dell'esploratore di file
pub struct FileExplorer {
    /// Core Engine
    core: Arc<CoreEngine>,

    /// Gestore dei temi
    theme_manager: Arc<ThemeManager>,

    /// Stato dell'esploratore
    state: FileExplorerState,
}

impl FileExplorer {
    /// Crea un nuovo esploratore di file
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> impl View {
        let current_dir = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));

        let state = FileExplorerState {
            current_path: create_rw_signal(current_dir.clone()),
            items: create_rw_signal(Vec::new()),
            selected: create_rw_signal(None),
            should_reload: create_rw_signal(true),
        };

        let explorer = Self {
            core: core.clone(),
            theme_manager,
            state,
        };

        let items = explorer.state.items;
        let should_reload = explorer.state.should_reload;
        let current_path = explorer.state.current_path;

        // Carica i file quando necessario
        create_memo(move |_| {
            if should_reload.get() {
                let path = current_path.get();
                let file_items = Self::load_directory(&path, 0);
                items.set(file_items);
                should_reload.set(false);
            }
        });

        let core_clone = core.clone();
        let selected = explorer.state.selected;

        v_stack((
            h_stack((
                label(move || "File Explorer".to_string())
                    .style(|s| s.font_weight(700).margin_bottom(8)), // Bold = 700
                button(|| "⟳".to_string())
                    .on_click(move |_| {
                        should_reload.set(true);
                        false
                    })
                    .style(|s| {
                        s.margin_left(0.0) // TODO: Replace auto() with proper Floem 0.2 equivalent
                         .padding(5)
                         .border_radius(4)
                    }),
            ))
            .style(|s| s.width_full().padding(8)),

            scroll(
                list(
                    move || items.get().into_iter().enumerate(),
                    |(index, item)| format!("{}_{}", index, item.path.display()),
                    move |(index, item)| {
                        let item_clone = item.clone();
                        let path = item.path.clone();
                        let is_dir = item.is_dir;
                        let depth = item.depth;

                        h_stack((
                            label(move || {
                                let indent = " ".repeat(depth * 2);
                                let icon = if is_dir {
                                    if item_clone.expanded { "📂 " } else { "📁 " }
                                } else {
                                    "📄 "
                                };
                                format!("{}{}{}", indent, icon, item_clone.name)
                            })
                            .style(move |s| {
                                s.width_full()
                                 .padding(8)
                                 .hover(|s| s.background(Color::rgb8(0x2A, 0x2A, 0x2A)))
                                 .active(|s| s.background(Color::rgb8(0x3A, 0x3A, 0x3A)))
                            })
                            .on_click(move |_| {
                                // Quando una directory viene cliccata, espandiamola o comprimiamola
                                if is_dir {
                                    Self::toggle_directory_expansion(&items, &path);
                                } else {
                                    // Quando un file viene cliccato, invia un evento
                                    selected.set(Some(items.get().iter().position(|i| i.path == path).unwrap_or(0)));

                                    // Invia l'evento di selezione del file
                                    let event_bus = core_clone.event_bus();
                                    let _ = event_bus.publish(FileSelectedEvent { path: path.clone() });
                                }
                                false
                            })
                        ))
                        .style(|s| s.width_full())
                    }
                )
                .style(|s| s.gap(0.0).width_full())
            )
            .style(|s| s.width_full().height_full())
        ))
        .style(move |s| {
            s.width_full()
             .height_full()
             .background(Color::rgb8(0x21, 0x21, 0x21))
             .color(Color::rgb8(0xE0, 0xE0, 0xE0))
        })
        .keyboard_navigatable()
        .on_event(EventListener::KeyDown, move |event| {
            if let Event::KeyDown(key_event) = event {
                if key_event.key == Key::Character("r".to_string()) && key_event.modifiers.contains(Modifiers::CONTROL) {
                    should_reload.set(true);
                    return true;
                }
            }
            false
        })
    }

    /// Carica una directory
    fn load_directory(path: &PathBuf, depth: usize) -> Vec<FileItem> {
        let mut items = Vec::new();

        if let Ok(entries) = std::fs::read_dir(path) {
            // Prima le directory, poi i file
            let mut dirs = Vec::new();
            let mut files = Vec::new();

            for entry in entries.flatten() {
                let entry_path = entry.path();
                let file_name = entry_path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("")
                    .to_string();

                // Ignora file nascosti
                if file_name.starts_with(".") {
                    continue;
                }

                let is_dir = entry_path.is_dir();
                let item = FileItem {
                    name: file_name,
                    path: entry_path.clone(),
                    is_dir,
                    expanded: false,
                    depth,
                    children: Vec::new(),
                };

                if is_dir {
                    dirs.push(item);
                } else {
                    files.push(item);
                }
            }

            // Ordina alfabeticamente
            dirs.sort_by(|a, b| a.name.to_lowercase().cmp(&b.name.to_lowercase()));
            files.sort_by(|a, b| a.name.to_lowercase().cmp(&b.name.to_lowercase()));

            // Combina directory e file
            items.extend(dirs);
            items.extend(files);
        }

        items
    }

    /// Espande o comprime una directory
    fn toggle_directory_expansion(items: &RwSignal<Vec<FileItem>>, path: &PathBuf) {
        let mut current_items = items.get();

        // Trova l'elemento da espandere/comprimere
        if let Some(index) = current_items.iter().position(|item| &item.path == path) {
            let item = &current_items[index];

            if item.is_dir {
                let expanded = !item.expanded;
                current_items[index].expanded = expanded;

                if expanded {
                    // Carica i figli
                    let children = Self::load_directory(path, item.depth + 1);

                    // Inserisci i figli dopo l'elemento
                    for (i, child) in children.into_iter().enumerate() {
                        current_items.insert(index + i + 1, child);
                    }
                } else {
                    // Rimuovi tutti i figli
                    let mut i = index + 1;
                    while i < current_items.len() {
                        if current_items[i].depth <= item.depth {
                            break;
                        }
                        current_items.remove(i);
                    }
                }

                items.set(current_items);
            }
        }
    }
}
