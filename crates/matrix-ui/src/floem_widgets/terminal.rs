//! Widget del terminale integrato
//!
//! Questo modulo fornisce un terminale integrato nell'IDE per eseguire comandi.

use std::sync::{Arc, Mutex};
use std::process::{Command, Stdio, Child};
use std::io::{<PERSON><PERSON><PERSON><PERSON><PERSON>, BufRead, Write};
use std::thread;

use floem::reactive::{RwSignal, create_rw_signal, create_effect, SignalGet, SignalUpdate};
use floem::views::*;
use floem::View;
use floem::style::*;
use floem::peniko::Color;
use floem::event::{Event, EventListener, EventPropagation};
use floem::keyboard::{Key, KeyEvent, Modifiers};

use matrix_core::Engine as CoreEngine;
use crate::theme::ThemeManager;

/// Stato del terminale
struct TerminalState {
    /// Righe di output
    output: RwSignal<Vec<String>>,

    /// Input corrente
    input: RwSignal<String>,

    /// Processo in esecuzione
    process: Arc<Mutex<Option<Child>>>,

    /// Working directory
    working_dir: RwSignal<String>,
}

/// Widget del terminale
pub struct Terminal {
    /// Core Engine
    core: Arc<CoreEngine>,

    /// Gestore dei temi
    theme_manager: Arc<ThemeManager>,

    /// Stato del terminale
    state: TerminalState,
}

impl Terminal {
    /// Crea un nuovo terminale
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> impl View {
        let current_dir = std::env::current_dir()
            .map(|path| path.to_string_lossy().to_string())
            .unwrap_or_else(|_| ".".to_string());

        let state = TerminalState {
            output: create_rw_signal(vec![format!("Terminale - Directory: {}", current_dir)]),
            input: create_rw_signal(String::new()),
            process: Arc::new(Mutex::new(None)),
            working_dir: create_rw_signal(current_dir),
        };

        let terminal = Self {
            core,
            theme_manager,
            state,
        };

        let output = terminal.state.output;
        let input = terminal.state.input;
        let process = terminal.state.process.clone();
        let working_dir = terminal.state.working_dir;

        v_stack((
            h_stack((
                label(move || "Terminale".to_string())
                    .style(|s| s.font_bold().margin_bottom(8)),
                label(move || format!("Cartella: {}", working_dir.get()))
                    .style(|s| s.margin_left(8).color(Color::rgb8(0xA0, 0xA0, 0xA0))),
                button(|| "Pulisci".to_string())
                    .action(move || {
                        output.update(|o| o.clear());
                    })
                    .style(|s| {
                        s.margin_left(0.0) // TODO: Replace auto() with proper Floem 0.2 equivalent
                         .padding(5)
                         .border_radius(4)
                    }),
            ))
            .style(|s| s.width_full().padding(8)),

            scroll(
                v_stack_from_iter(
                    output.get().into_iter().enumerate().map(move |(index, line)| {
                        label(move || line.clone())
                            .style(|s| s.width_full().font_family("monospace"))
                    })
                )
                .style(|s| s.gap(4.0).width_full())
            )
            .style(|s| {
                s.width_full()
                 .height_full() // TODO: Replace BoxConstraints/stretch with proper Floem 0.2 equivalent
                 .background(Color::rgb8(0x18, 0x18, 0x18))
                 .padding(8)
                 .border(1)
                 .border_color(Color::rgb8(0x30, 0x30, 0x30))
                 .border_radius(4)
            }),

            h_stack((
                text_input(input)
                    .on_submit(move |value| {
                        if !value.trim().is_empty() {
                            Self::execute_command(
                                value.clone(),
                                output,
                                process.clone(),
                                working_dir.get()
                            );
                            input.set(String::new());
                        }
                    })
                    .style(|s| {
                        s.width_full()
                         .padding(8)
                         .border(1)
                         .border_color(Color::rgb8(0x30, 0x30, 0x30))
                         .border_radius(4)
                         .background(Color::rgb8(0x22, 0x22, 0x22))
                         .font_family("monospace")
                    }),

                button(|| "Esegui".to_string())
                    .on_click(move |_| {
                        let cmd = input.get();
                        if !cmd.trim().is_empty() {
                            Self::execute_command(
                                cmd.clone(),
                                output,
                                process.clone(),
                                working_dir.get()
                            );
                            input.set(String::new());
                        }
                        EventPropagation::Continue
                    })
                    .style(|s| {
                        s.margin_left(8)
                         .padding(8)
                         .border_radius(4)
                    }),
            ))
            .style(|s| s.width_full().padding(8))
        ))
        .style(move |s| {
            s.width_full()
             .height_full()
             .background(Color::rgb8(0x21, 0x21, 0x21))
             .color(Color::rgb8(0xE0, 0xE0, 0xE0))
        })
        .keyboard_navigable()
        .on_event(EventListener::KeyDown, move |event| {
            if let Event::KeyDown(key_event) = event {
                if key_event.key == Key::Character("c".to_string()) && 
                   key_event.modifiers.contains(Modifiers::CONTROL) {
                    // Ctrl+C - termina il processo in esecuzione
                    if let Some(mut child) = process.lock().unwrap().take() {
                        let _ = child.kill();
                        output.update(|o| o.push("Processo terminato.".to_string()));
                    }
                    return true;
                }
            }
            false
        })
    }

    /// Esegue un comando nel terminale
    fn execute_command(
        cmd: String, 
        output: RwSignal<Vec<String>>, 
        process: Arc<Mutex<Option<Child>>>,
        working_dir: String
    ) {
        output.update(|o| o.push(format!("> {}", cmd)));

        // Analizza il comando (gestendo semplici comandi built-in)
        if cmd.trim() == "clear" || cmd.trim() == "cls" {
            output.update(|o| o.clear());
            return;
        }

        if cmd.trim().starts_with("cd ") {
            let new_dir = cmd.trim()[3..].trim();
            // Implementazione semplificata del cambio directory
            // In un'implementazione reale, dovrebbe gestire path relativi, ~, ecc.
            let path = std::path::Path::new(&working_dir).join(new_dir);
            if path.exists() && path.is_dir() {
                *process.lock().unwrap() = None;
                output.update(|o| o.push(format!("Cambiata directory: {}", path.display())));
                return;
            } else {
                output.update(|o| o.push(format!("Directory non trovata: {}", path.display())));
                return;
            }
        }

        // Esegui comandi esterni
        let parts: Vec<&str> = cmd.split_whitespace().collect();
        if parts.is_empty() {
            return;
        }

        let program = parts[0];
        let args = &parts[1..];

        // Selezione del comando in base al sistema operativo
        let (cmd_name, cmd_args) = if cfg!(target_os = "windows") {
            ("cmd", vec!["/C", &cmd])
        } else {
            ("sh", vec!["-c", &cmd])
        };

        match Command::new(cmd_name)
            .args(cmd_args)
            .current_dir(&working_dir)
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .spawn() 
        {
            Ok(child) => {
                let stdout = child.stdout.expect("Failed to capture stdout");
                let stderr = child.stderr.expect("Failed to capture stderr");

                let output_clone = output.clone();
                let stdout_reader = BufReader::new(stdout);

                // Gestione dell'output standard
                thread::spawn(move || {
                    for line in stdout_reader.lines() {
                        if let Ok(line) = line {
                            output_clone.update(|o| o.push(line));
                        }
                    }
                });

                let output_clone = output.clone();
                let stderr_reader = BufReader::new(stderr);

                // Gestione dell'errore standard
                thread::spawn(move || {
                    for line in stderr_reader.lines() {
                        if let Ok(line) = line {
                            output_clone.update(|o| o.push(format!("Error: {}", line)));
                        }
                    }
                });

                // Memorizza il processo in esecuzione
                *process.lock().unwrap() = Some(child);
            }
            Err(e) => {
                output.update(|o| o.push(format!("Errore nell'esecuzione del comando: {}", e)));
            }
        }
    }
}
