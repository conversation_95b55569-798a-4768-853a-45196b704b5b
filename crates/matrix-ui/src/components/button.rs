//! Componente pulsante personalizzato
//!
//! Questo modulo fornisce un pulsante personalizzato per l'IDE MATRIX.

use std::sync::Arc;
use floem::{View, views::{button, Decorators}};
use floem::style::Style;
use floem::text::Weight;
use floem::event::EventPropagation;
use crate::theme::{Theme, ThemeManager};

/// Tipi di pulsante
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ButtonType {
    /// Pulsante primario
    Primary,

    /// Pulsante secondario
    Secondary,

    /// Pulsante di successo
    Success,

    /// Pulsante di pericolo
    Danger,

    /// Pulsante di informazione
    Info,

    /// Pulsante di avviso
    Warning,
}

/// Dimensione del pulsante
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ButtonSize {
    /// Pulsante piccolo
    Small,

    /// Pulsante medio
    Medium,

    /// Pulsante grande
    Large,
}

/// Crea un pulsante personalizzato
pub fn matrix_button<F, V>(
    theme_manager: Arc<ThemeManager>,
    label: impl Fn() -> String + 'static,
    on_click: F,
    button_type: ButtonType,
    button_size: ButtonSize,
) -> impl View
where
    F: FnMut() -> V + 'static,
    V: View + 'static,
{
    let theme = theme_manager.get_active_theme().unwrap_or_default();

    button(label)
        .on_click(move |_| {
            let _view = on_click();
            EventPropagation::Stop
        })
        .style(move |s| {
            let base_style = s
                .font_weight(Weight::MEDIUM)
                .border_radius(theme.borders.radius_medium);

            // Aggiungi padding in base alla dimensione
            let style_with_size = match button_size {
                ButtonSize::Small => base_style.padding_horiz(8.0).padding_vert(4.0).font_size(theme.font_sizes.sm),
                ButtonSize::Medium => base_style.padding_horiz(12.0).padding_vert(6.0).font_size(theme.font_sizes.md),
                ButtonSize::Large => base_style.padding_horiz(16.0).padding_vert(8.0).font_size(theme.font_sizes.lg),
            };

            // Aggiungi colori in base al tipo
            match button_type {
                ButtonType::Primary => style_with_size
                    .background(theme.colors.accent)
                    .color(theme.colors.text)
                    .hover(|s| s.background(theme.colors.accent_secondary))
                    .active(|s| s.background(theme.colors.accent))
                    .disabled(|s| s.color(theme.colors.text.multiply_alpha(0.6))),

                ButtonType::Secondary => style_with_size
                    .background(theme.colors.background_secondary)
                    .color(theme.colors.text)
                    .hover(|s| s.background(theme.colors.background_tertiary))
                    .active(|s| s.background(theme.colors.background_secondary))
                    .disabled(|s| s.color(theme.colors.text.multiply_alpha(0.6))),

                ButtonType::Success => style_with_size
                    .background(theme.colors.success)
                    .color(theme.colors.text)
                    .hover(|s| s.background(theme.colors.success.with_alpha_factor(0.8)))
                    .active(|s| s.background(theme.colors.success))
                    .disabled(|s| s.color(theme.colors.text.multiply_alpha(0.6))),

                ButtonType::Danger => style_with_size
                    .background(theme.colors.error)
                    .color(theme.colors.text)
                    .hover(|s| s.background(theme.colors.error.with_alpha_factor(0.8)))
                    .active(|s| s.background(theme.colors.error))
                    .disabled(|s| s.color(theme.colors.text.multiply_alpha(0.6))),

                ButtonType::Info => style_with_size
                    .background(theme.colors.info)
                    .color(theme.colors.text)
                    .hover(|s| s.background(theme.colors.info.with_alpha_factor(0.8)))
                    .active(|s| s.background(theme.colors.info))
                    .disabled(|s| s.color(theme.colors.text.multiply_alpha(0.6))),

                ButtonType::Warning => style_with_size
                    .background(theme.colors.warning)
                    .color(theme.colors.text)
                    .hover(|s| s.background(theme.colors.warning.with_alpha(0.8)))
                    .active(|s| s.background(theme.colors.warning))
                    .disabled(|s| s.opacity(0.6)),
            }
        })
}

/// Crea un pulsante primario
pub fn primary_button<F, V>(
    theme_manager: Arc<ThemeManager>,
    label: impl Fn() -> String + 'static,
    on_click: F,
) -> impl View
where
    F: FnMut() -> V + 'static,
    V: View + 'static,
{
    matrix_button(
        theme_manager,
        label,
        on_click,
        ButtonType::Primary,
        ButtonSize::Medium,
    )
}

/// Crea un pulsante secondario
pub fn secondary_button<F, V>(
    theme_manager: Arc<ThemeManager>,
    label: impl Fn() -> String + 'static,
    on_click: F,
) -> impl View
where
    F: FnMut() -> V + 'static,
    V: View + 'static,
{
    matrix_button(
        theme_manager,
        label,
        on_click,
        ButtonType::Secondary,
        ButtonSize::Medium,
    )
}
