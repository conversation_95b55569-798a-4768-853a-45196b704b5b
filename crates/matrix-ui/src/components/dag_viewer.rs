//! Visualizzatore di grafi DAG per MATRIX IDE
//!
//! Questo modulo fornisce un componente per la visualizzazione interattiva
//! dei grafi DAG (Directed Acyclic Graph) utilizzati nel sistema.

use std::sync::{Arc, RwLock};
use std::collections::{HashMap, HashSet};

use floem::peniko::Color;
use floem::reactive::{create_rw_signal, create_memo, RwSignal, Memo, SignalGet, SignalUpdate};
use floem::{View, views::{container, h_stack, label, scroll, svg, v_stack, Decorators}};
use floem::style::Style;
use floem::event::{Event as FloemEvent, EventListener};

use matrix_core::dag_engine::{DagGraph, Task, TaskState};
use matrix_core::Engine as CoreEngine;
use matrix_core::EventBus;

use crate::error::UiError;
use crate::theme::ThemeManager;

/// Posizione di un nodo nel layout
#[derive(Debug, Clone, Copy)]
struct NodePosition {
    x: f64,
    y: f64,
}

/// Dimensioni di un nodo
#[derive(Debug, Clone, Copy)]
struct NodeSize {
    width: f64,
    height: f64,
}

/// Informazioni di layout per un nodo
#[derive(Debug, Clone)]
pub struct NodeLayoutInfo {
    /// Posizione del nodo
    pub position: NodePosition,
    /// Dimensioni del nodo
    pub size: NodeSize,
    /// Se il nodo è visibile
    pub visible: bool,
    /// Livello del nodo nel layout gerarchico
    pub level: usize,
    /// Indice del nodo nel suo livello
    pub index_in_level: usize,
}

impl NodeLayoutInfo {
    /// Crea nuove informazioni di layout per un nodo
    pub fn new(position: NodePosition, size: NodeSize) -> Self {
        Self {
            position,
            size,
            visible: true,
            level: 0,
            index_in_level: 0,
        }
    }

    /// Aggiorna la posizione del nodo
    pub fn set_position(&mut self, position: NodePosition) {
        self.position = position;
    }

    /// Aggiorna le dimensioni del nodo
    pub fn set_size(&mut self, size: NodeSize) {
        self.size = size;
    }

    /// Imposta la visibilità del nodo
    pub fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }
}

/// Motore di layout per il grafo
struct GraphLayoutEngine {
    /// Spaziatura orizzontale tra i nodi
    horizontal_spacing: f64,
    /// Spaziatura verticale tra i livelli
    vertical_spacing: f64,
    /// Larghezza predefinita dei nodi
    node_width: f64,
    /// Altezza predefinita dei nodi
    node_height: f64,
}

impl GraphLayoutEngine {
    /// Crea un nuovo motore di layout con valori predefiniti
    pub fn new() -> Self {
        Self {
            horizontal_spacing: 150.0,
            vertical_spacing: 100.0,
            node_width: 120.0,
            node_height: 60.0,
        }
    }

    /// Calcola il layout del grafo
    pub fn calculate_layout(&self, graph: &DagGraph) -> HashMap<String, NodeLayoutInfo> {
        let mut layout = HashMap::new();

        // Ottiene l'ordinamento topologico
        let ordering = match graph.topological_sort() {
            Ok(order) => order,
            Err(_) => return layout, // In caso di errore restituisce un layout vuoto
        };

        // Raggruppa i nodi per livello (distanza dalla radice)
        let mut levels: HashMap<usize, Vec<String>> = HashMap::new();
        let mut node_level: HashMap<String, usize> = HashMap::new();

        // Calcola il livello di ogni nodo
        for task_id in ordering {
            let task = match graph.get_task(&task_id) {
                Some(t) => t,
                None => continue,
            };

            let level = task.dependencies.iter()
                .filter_map(|dep_id| node_level.get(dep_id))
                .map(|level| level + 1)
                .max()
                .unwrap_or(0);

            node_level.insert(task_id.clone(), level);
            levels.entry(level).or_insert_with(Vec::new).push(task_id);
        }

        // Calcola le posizioni dei nodi per ogni livello
        let max_level = levels.keys().max().copied().unwrap_or(0);

        for level in 0..=max_level {
            let nodes = levels.get(&level).unwrap_or(&Vec::new());
            let level_width = nodes.len() as f64 * (self.node_width + self.horizontal_spacing);
            let start_x = -level_width / 2.0 + self.node_width / 2.0;

            for (i, task_id) in nodes.iter().enumerate() {
                let x = start_x + i as f64 * (self.node_width + self.horizontal_spacing);
                let y = level as f64 * (self.node_height + self.vertical_spacing);

                layout.insert(task_id.clone(), NodeLayoutInfo {
                    id: task_id.clone(),
                    position: NodePosition { x, y },
                    size: NodeSize { 
                        width: self.node_width, 
                        height: self.node_height 
                    },
                    selected: false,
                    highlighted: false,
                });
            }
        }

        layout
    }
}

/// Visualizzatore di grafi DAG
#[derive(Clone)]
pub struct DagViewer {
    /// Core Engine
    core: Arc<CoreEngine>,
    /// Theme Manager
    theme_manager: Arc<ThemeManager>,
    /// Layout engine per il grafo
    layout_engine: GraphLayoutEngine,
    /// ID del grafo corrente
    current_graph_id: RwSignal<Option<String>>,
    /// Layout dei nodi
    node_layout: RwSignal<HashMap<String, NodeLayoutInfo>>,
    /// Nodo selezionato
    selected_node: RwSignal<Option<String>>,
    /// Zoom level
    zoom_level: RwSignal<f64>,
    /// Offset della vista
    view_offset: RwSignal<(f64, f64)>,
    /// Se il grafo è in modalità di modifica
    edit_mode: RwSignal<bool>,
}

impl DagViewer {
    /// Crea un nuovo visualizzatore DAG
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> Result<Arc<Self>, UiError> {
        let current_graph_id = create_rw_signal(None);
        let node_layout = create_rw_signal(HashMap::new());
        let selected_node = create_rw_signal(None);
        let zoom_level = create_rw_signal(1.0);
        let view_offset = create_rw_signal((0.0, 0.0));
        let edit_mode = create_rw_signal(false);

        let layout_engine = GraphLayoutEngine::new();

        let viewer = Self {
            core,
            theme_manager,
            layout_engine,
            current_graph_id,
            node_layout,
            selected_node,
            zoom_level,
            view_offset,
            edit_mode,
        };

        Ok(Arc::new(viewer))
    }

    /// Ottiene l'ID del grafo corrente
    pub fn current_graph_id(&self) -> Option<String> {
        self.current_graph_id.get()
    }

    /// Crea la vista del visualizzatore DAG
    pub fn create_view(&self) -> impl View {
        // Placeholder per la vista - implementazione completa richiede correzioni API Floem 0.2
        container(
            label(|| "DAG Viewer - Implementation in progress")
        )
        .style(|s| s.size_full().justify_center().align_center())
    }

    /// Imposta il grafo corrente
    pub fn set_graph(&self, graph_id: String) -> Result<(), UiError> {
        self.current_graph_id.set(Some(graph_id));
        self.update_layout()?;
        Ok(())
    }

    /// Aggiorna il layout del grafo
    pub fn update_layout(&self) -> Result<(), UiError> {
        let graph = match self.current_graph.get() {
            Some(g) => g,
            None => {
                self.node_layout.set(HashMap::new());
                return Ok(());
            }
        };

        let graph_guard = graph.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on graph".to_string())
        })?;

        let layout = self.layout_engine.calculate_layout(&graph_guard);
        self.node_layout.set(layout);

        Ok(())
    }

    /// Seleziona un nodo
    pub fn select_node(&self, node_id: Option<String>) {
        self.selected_node.set(node_id);
    }

    /// Disegna un nodo del grafo
    fn draw_node(&self, task: &Task, layout: &NodeLayoutInfo) -> impl View {
        let id = task.id.clone();
        let state = task.state;
        let name = task.metadata.name.clone();
        let selected = layout.selected;
        let highlighted = layout.highlighted;

        let task_type = task.metadata.task_type.clone();

        // Determina il colore in base allo stato
        let fill_color = match state {
            TaskState::Pending => Color::rgb8(0x41, 0x41, 0x41),    // Grigio scuro
            TaskState::Running => Color::rgb8(0x00, 0x87, 0xE8),    // Blu
            TaskState::Completed => Color::rgb8(0x28, 0xA7, 0x45),  // Verde
            TaskState::Failed => Color::rgb8(0xDC, 0x30, 0x30),     // Rosso
            TaskState::Cancelled => Color::rgb8(0x88, 0x88, 0x88),  // Grigio chiaro
        };

        // Stile del bordo in base alla selezione
        let border_width = if selected { 3.0 } else if highlighted { 2.0 } else { 1.0 };
        let border_color = if selected {
            Color::rgb8(0xFF, 0xC1, 0x07) // Giallo per selezione
        } else if highlighted {
            Color::rgb8(0xFF, 0xE0, 0x82) // Giallo chiaro per hover
        } else {
            Color::rgb8(0x66, 0x66, 0x66) // Grigio per default
        };

        let selected_node = self.selected_node.clone();
        let highlighted_node = self.highlighted_node.clone();
        let node_id = id.clone();

        // Contenitore del nodo con interazioni
        container(
            v_stack((
                label(move || name.clone())
                    .style(|s| s.color(Color::WHITE).font_size(12.0).margin(2.0)),
                label(move || task_type.clone())
                    .style(|s| s.color(Color::rgba8(255, 255, 255, 180)).font_size(10.0).margin(2.0)),
            ))
            .style(|s| s.align_items(floem::style::AlignItems::Center).justify_content(floem::style::JustifyContent::Center))
        )
        .style(move |s| {
            let x = layout.position.x;
            let y = layout.position.y;
            let width = layout.size.width;
            let height = layout.size.height;

            s.position(floem::style::Position::Absolute)
                .left(x)
                .top(y)
                .width(width)
                .height(height)
                .background(fill_color)
                .border(border_width)
                .border_color(border_color)
                .border_radius(4.0)
                .cursor(floem::style::Cursor::Pointer)
        })
        .on_event(FloemEvent::PointerEnter, move |_| {
            highlighted_node.set(Some(node_id.clone()));
            true
        })
        .on_event(FloemEvent::PointerLeave, move |_| {
            highlighted_node.set(None);
            true
        })
        .on_event(FloemEvent::PointerDown, move |_| {
            selected_node.set(Some(node_id.clone()));
            true
        })
    }

    /// Crea la vista degli archi tra i nodi
    fn create_edge_view(&self, from: &NodeLayoutInfo, to: &NodeLayoutInfo) -> impl View {
        let scale = self.scale;
        let offset_x = self.offset_x;
        let offset_y = self.offset_y;
        let selected_node = self.selected_node;
        let highlighted_node = self.highlighted_node;

        let from_x = from.position.x * scale.get() + offset_x.get() + from.size.width / 2.0;
        let from_y = from.position.y * scale.get() + offset_y.get() + from.size.height;
        let to_x = to.position.x * scale.get() + offset_x.get() + to.size.width / 2.0;
        let to_y = to.position.y * scale.get() + offset_y.get();
        let to_y = to.position.y * scale.get() + offset_y.get();

        // Calcola i punti di controllo per la curva Bezier
        let control_y = (from_y + to_y) / 2.0;

        // Crea la path SVG
        let path = format!(
            "M {},{} C {},{} {},{} {},{}",
            from_x, from_y,
            from_x, control_y,
            to_x, control_y,
            to_x, to_y
        );

        svg(move || {
            let mut svg = floem::views::svg::Svg::new();

            let selected = selected_node.get().map(|id| id == from.id || id == to.id).unwrap_or(false);
            let highlighted = highlighted_node.get().map(|id| id == from.id || id == to.id).unwrap_or(false);

            // Stile dell'arco in base alla selezione
            let stroke_width = if selected { 2.5 } else if highlighted { 2.0 } else { 1.5 };
            let stroke_color = if selected {
                Color::rgb8(0xFF, 0xC1, 0x07) // Giallo per selezione
            } else if highlighted {
                Color::rgb8(0xFF, 0xE0, 0x82) // Giallo chiaro per hover
            } else {
                Color::rgb8(0x99, 0x99, 0x99) // Grigio per default
            };

            svg.add(
                floem::views::svg::Path::new()
                    .attr("d", path)
                    .attr("fill", "none")
                    .attr("stroke", format!("#{:02x}{:02x}{:02x}", 
                                         stroke_color.r(), 
                                         stroke_color.g(), 
                                         stroke_color.b()))
                    .attr("stroke-width", stroke_width.to_string())
            );

            // Aggiungi una freccia alla fine dell'arco
            let arrow_size = 6.0;
            let angle = (to_y - control_y).atan2(to_x - from_x);
            let arrow_x1 = to_x - arrow_size * (angle - 0.5).cos();
            let arrow_y1 = to_y - arrow_size * (angle - 0.5).sin();
            let arrow_x2 = to_x - arrow_size * (angle + 0.5).cos();
            let arrow_y2 = to_y - arrow_size * (angle + 0.5).sin();

            svg.add(
                floem::views::svg::Path::new()
                    .attr("d", format!("M {},{} L {},{} L {},{} Z", 
                                      to_x, to_y, 
                                      arrow_x1, arrow_y1, 
                                      arrow_x2, arrow_y2))
                    .attr("fill", format!("#{:02x}{:02x}{:02x}", 
                                       stroke_color.r(), 
                                       stroke_color.g(), 
                                       stroke_color.b()))
            );

            svg
        })
        .style(|s| s.position(floem::style::Position::Absolute).size_full())
    }

    /// Crea il canvas per disegnare il grafo
    fn create_canvas(&self) -> impl View {
        let node_layout = self.node_layout;
        let current_graph = self.current_graph.clone();
        let scale = self.scale;
        let offset_x = self.offset_x;
        let offset_y = self.offset_y;
        let selected_node = self.selected_node;
        let highlighted_node = self.highlighted_node;
        let canvas_width = self.canvas_width;
        let canvas_height = self.canvas_height;
        let self_clone = self.clone();

        // Memo per gli archi (edges) del grafo
        let edges = create_memo(move |_| {
            let graph = match current_graph.get() {
                Some(g) => g,
                None => return Vec::new(),
            };

            let graph_guard = match graph.read() {
                Ok(g) => g,
                Err(_) => return Vec::new(),
            };

            let layout = node_layout.get();
            let mut edges = Vec::new();

            // Crea gli archi in base alle dipendenze
            for (task_id, layout_info) in &layout {
                let task = match graph_guard.get_task(task_id) {
                    Some(t) => t,
                    None => continue,
                };

                for dep_id in &task.dependencies {
                    if let Some(dep_layout) = layout.get(dep_id) {
                        edges.push((dep_layout.clone(), layout_info.clone()));
                    }
                }
            }

            edges
        });

        // Container principale
        scroll(
            container(
                // Layer per gli archi
                container(
                    svg(move || {
                        let mut svg = floem::views::svg::Svg::new();
                        svg.set_viewbox(0.0, 0.0, canvas_width.get(), canvas_height.get());
                        svg
                    })
                    .style(|s| s.size_full())
                    .children_fn(move || {
                        edges.get().into_iter().map(|(from, to)| self_clone.create_edge_view(&from, &to).any()).collect()
                    })
                )
                .style(|s| s.size_full().z_index(1))

                // Layer per i nodi
                .child(
                    container(floem::views::empty())
                        .style(|s| s.size_full().z_index(2))
                        .children_fn(move || {
                            let layout = node_layout.get();
                            let selected_id = selected_node.get();
                            let highlighted_id = highlighted_node.get();

                            // Aggiorna lo stato di selezione e highlight
                            let mut updated_layout = layout.clone();
                            for (id, info) in &mut updated_layout {
                                info.selected = selected_id.as_ref().map_or(false, |sel_id| sel_id == id);
                                info.highlighted = highlighted_id.as_ref().map_or(false, |hl_id| hl_id == id);
                            }

                            // Ottieni il grafo
                            let graph = match current_graph.get() {
                                Some(g) => g,
                                None => return vec![],
                            };

                            let graph_guard = match graph.read() {
                                Ok(g) => g,
                                Err(_) => return vec![],
                            };

                            // Crea i nodi
                            updated_layout.iter()
                                .filter_map(|(id, layout_info)| {
                                    graph_guard.get_task(id).map(|task| {
                                        let node = self_clone.draw_node(task, layout_info);
                                        node.any()
                                    })
                                })
                                .collect()
                        })
                )
            )
            .style(|s| s.size_full().background(Color::rgb8(0x1E, 0x1E, 0x1E)))
            .on_event(FloemEvent::PointerWheel, move |event| {
                if let FloemEvent::PointerWheel(pointer_event) = event {
                    // Zoom con la rotella del mouse
                    let delta = pointer_event.delta_y;
                    let old_scale = scale.get();
                    let new_scale = (old_scale * (1.0 - delta * 0.001)).max(0.2).min(5.0);
                    scale.set(new_scale);

                    // Adatta l'offset per mantenere il punto sotto il cursore
                    let mouse_x = pointer_event.pos.x;
                    let mouse_y = pointer_event.pos.y;
                    let dx = mouse_x - offset_x.get();
                    let dy = mouse_y - offset_y.get();
                    let factor = new_scale / old_scale;

                    offset_x.set(mouse_x - dx * factor);
                    offset_y.set(mouse_y - dy * factor);

                    true
                } else {
                    false
                }
            })
            .on_event(FloemEvent::PointerDown, move |event| {
                if let FloemEvent::PointerDown(pointer_event) = event {
                    // Verifica se il click è sul background (non su un nodo)
                    if pointer_event.target == pointer_event.current {
                        // Inizia il panning
                        // Nota: qui dovresti implementare il tracking dello stato del panning
                        // e gestire gli eventi PointerMove e PointerUp per completare il panning
                        true
                    } else {
                        false
                    }
                } else {
                    false
                }
            })
        )
        .style(|s| s.size_full().background(Color::rgb8(0x1E, 0x1E, 0x1E)))
    }

    /// Gestisce lo zoom del grafo
    pub fn zoom(&self, delta: f64, center_x: f64, center_y: f64) {
        let old_scale = self.scale.get();
        let new_scale = (old_scale * (1.0 + delta * 0.001)).max(0.2).min(5.0);
        self.scale.set(new_scale);

        // Adatta l'offset per mantenere il centro dello zoom
        let dx = center_x - self.offset_x.get();
        let dy = center_y - self.offset_y.get();
        let factor = new_scale / old_scale;

        self.offset_x.set(center_x - dx * factor);
        self.offset_y.set(center_y - dy * factor);
    }

    /// Gestisce il panning del grafo
    pub fn pan(&self, dx: f64, dy: f64) {
        self.offset_x.update(|x| *x += dx);
        self.offset_y.update(|y| *y += dy);
    }

    /// Resetta la vista del grafo
    pub fn reset_view(&self) {
        self.scale.set(1.0);
        self.offset_x.set(self.canvas_width.get() / 2.0);
        self.offset_y.set(50.0); // Margine dall'alto
        self.update_layout().ok();
    }
    
    /// Esporta il grafo come immagine
    pub fn export_to_image(&self) -> Result<String, UiError> {
        // 1. Prepara il grafo per l'esportazione
        if let Err(e) = self.update_layout() {
            return Err(UiError::OperationError(format!("Errore nella preparazione del grafo: {}", e)));
        }
        
        // 2. Crea un'immagine del grafo attuale
        // In un'implementazione reale, qui utilizzeremmo una libreria
        // per il rendering del grafo come immagine, ad esempio:
        // - Cairo per il rendering vettoriale
        // - Una libreria per catturare il contenuto del canvas come image/png
        
        // 3. Salva l'immagine su file o esportala
        // Esempio di percorso del file di output
        let output_path = match self.current_graph_id.get() {
            Some(id) => format!("/tmp/graph_{}.png", id),
            None => "/tmp/graph_export.png".to_string()
        };
        
        // 4. Restituisci un Result con il percorso del file o un errore
        // Per ora, simuliamo l'implementazione restituendo un errore descrittivo
        Err(UiError::NotImplemented(
            "La funzionalità di esportazione dell'immagine richiede l'integrazione con una libreria di rendering grafico".to_string()
        ))
        
        // Quando l'implementazione sarà completata:
        // Ok(output_path)
    }

    /// Crea la vista del visualizzatore DAG
    pub fn create_view(&self) -> impl View {
        let selected_node = self.selected_node;
        let current_graph_id = self.current_graph_id;
        let self_clone = self.clone();

        v_stack((
            // Barra degli strumenti
            h_stack((
                label(move || {
                    let graph_id = current_graph_id.get();
                    if let Some(id) = graph_id {
                        format!("Grafo: {}", id)
                    } else {
                        "Nessun grafo selezionato".to_string()
                    }
                })
                .style(|s| s.font_weight(700).margin(8.0)), // Bold = 700

                label(move || {
                    let node_id = selected_node.get();
                    if let Some(id) = node_id {
                        format!("Nodo selezionato: {}", id)
                    } else {
                        "Nessun nodo selezionato".to_string()
                    }
                })
                .style(|s| s.margin(8.0)),

                floem::views::button("Reset View")
                    .on_click(move |_| {
                        self_clone.reset_view();
                    })
                    .style(|s| s.margin(8.0)),

                floem::views::button("Zoom In")
                    .on_click(move |_| {
                        let center_x = self_clone.canvas_width.get() / 2.0;
                        let center_y = self_clone.canvas_height.get() / 2.0;
                        self_clone.zoom(100.0, center_x, center_y);
                    })
                    .style(|s| s.margin(8.0)),

                floem::views::button("Zoom Out")
                    .on_click(move |_| {
                        let center_x = self_clone.canvas_width.get() / 2.0;
                        let center_y = self_clone.canvas_height.get() / 2.0;
                        self_clone.zoom(-100.0, center_x, center_y);
                    })
                    .style(|s| s.margin(8.0)),
                
                floem::views::button("Refresh")
                    .on_click(move |_| {
                        self_clone.update_layout().ok();
                    })
                    .style(|s| s.margin(8.0)),
            ))
            .style(|s| s.width_full().height(50.0).background(Color::rgb8(0x30, 0x30, 0x30))),

            // Canvas del grafo
            self.create_canvas(),
        ))
        .style(|s| s.size_full().background(Color::rgb8(0x21, 0x21, 0x21)))
    }
}
//
// Questo modulo fornisce un componente per la visualizzazione interattiva
// dei grafi DAG (Directed Acyclic Graph) utilizzati nel sistema.




impl GraphLayoutEngine {
    /// Crea un nuovo motore di layout con valori predefiniti
    pub fn new() -> Self {
        Self {
            horizontal_spacing: 150.0,
            vertical_spacing: 100.0,
            node_width: 120.0,
            node_height: 60.0,
        }
    }

    /// Calcola il layout del grafo
    pub fn calculate_layout(&self, graph: &DagGraph) -> HashMap<String, NodeLayoutInfo> {
        let mut layout = HashMap::new();

        // Ottiene l'ordinamento topologico
        let ordering = match graph.topological_sort() {
            Ok(order) => order,
            Err(_) => return layout, // In caso di errore restituisce un layout vuoto
        };

        // Raggruppa i nodi per livello (distanza dalla radice)
        let mut levels: HashMap<usize, Vec<String>> = HashMap::new();
        let mut node_level: HashMap<String, usize> = HashMap::new();

        // Calcola il livello di ogni nodo
        for task_id in ordering {
            let task = match graph.get_task(&task_id) {
                Some(t) => t,
                None => continue,
            };

            let level = task.dependencies.iter()
                .filter_map(|dep_id| node_level.get(dep_id))
                .map(|level| level + 1)
                .max()
                .unwrap_or(0);

            node_level.insert(task_id.clone(), level);
            levels.entry(level).or_insert_with(Vec::new).push(task_id);
        }

        // Calcola le posizioni dei nodi per ogni livello
        let max_level = levels.keys().max().copied().unwrap_or(0);

        for level in 0..=max_level {
            let nodes = levels.get(&level).unwrap_or(&Vec::new());
            let level_width = nodes.len() as f64 * (self.node_width + self.horizontal_spacing);
            let start_x = -level_width / 2.0 + self.node_width / 2.0;

            for (i, task_id) in nodes.iter().enumerate() {
                let x = start_x + i as f64 * (self.node_width + self.horizontal_spacing);
                let y = level as f64 * (self.node_height + self.vertical_spacing);

                layout.insert(task_id.clone(), NodeLayoutInfo {
                    id: task_id.clone(),
                    position: NodePosition { x, y },
                    size: NodeSize { 
                        width: self.node_width, 
                        height: self.node_height 
                    },
                    selected: false,
                    highlighted: false,
                });
            }
        }

        layout
    }
}

/// Visualizzatore di grafi DAG

impl DagViewer {
    /// Crea un nuovo visualizzatore DAG
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> Result<Self, UiError> {
        let current_graph_id = create_rw_signal(None);
        let node_layout = create_rw_signal(HashMap::new());
        let selected_node = create_rw_signal(None);
        let highlighted_node = create_rw_signal(None);
        let scale = create_rw_signal(1.0);
        let offset_x = create_rw_signal(0.0);
        let offset_y = create_rw_signal(0.0);
        let canvas_width = create_rw_signal(800.0);
        let canvas_height = create_rw_signal(600.0);

        let layout_engine = GraphLayoutEngine::new();

        // Memo per ottenere il grafo corrente
        let core_clone = core.clone();
        let current_graph = create_memo(move |_| {
            let graph_id = current_graph_id.get()?;
            match core_clone.dag_engine().get_graph(&graph_id) {
                Ok(graph) => Some(graph),
                Err(_) => None,
            }
        });

        let viewer = Self {
            core,
            theme_manager,
            current_graph_id,
            node_layout,
            selected_node,
            highlighted_node,
            layout_engine,
            scale,
            offset_x,
            offset_y,
            current_graph,
            canvas_width,
            canvas_height,
        };

        Ok(viewer)
    }

    /// Imposta il grafo corrente
    pub fn set_graph(&self, graph_id: String) -> Result<(), UiError> {
        self.current_graph_id.set(Some(graph_id));
        self.update_layout()?;
        Ok(())
    }

    /// Aggiorna il layout del grafo
    pub fn update_layout(&self) -> Result<(), UiError> {
        let graph = match self.current_graph.get() {
            Some(g) => g,
            None => {
                self.node_layout.set(HashMap::new());
                return Ok(());
            }
        };

        let graph_guard = graph.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on graph".to_string())
        })?;

        let layout = self.layout_engine.calculate_layout(&graph_guard);
        self.node_layout.set(layout);

        Ok(())
    }

    /// Seleziona un nodo
    pub fn select_node(&self, node_id: Option<String>) {
        self.selected_node.set(node_id);
    }

    /// Crea la vista del visualizzatore DAG
    pub fn create_view(&self) -> impl View {
        let selected_node = self.selected_node;
        let current_graph_id = self.current_graph_id;
        let self_clone = self.clone();

        v_stack((
            // Barra degli strumenti
            h_stack((
                label(move || {
                    let graph_id = current_graph_id.get();
                    if let Some(id) = graph_id {
                        format!("Grafo: {}", id)
                    } else {
                        "Nessun grafo selezionato".to_string()
                    }
                })
                .style(|s| s.font_weight(floem::style::FontWeight::Bold).margin(8.0)),

                label(move || {
                    let node_id = selected_node.get();
                    if let Some(id) = node_id {
                        format!("Nodo selezionato: {}", id)
                    } else {
                        "Nessun nodo selezionato".to_string()
                    }
                })
                .style(|s| s.margin(8.0)),

                floem::views::button("Reset View")
                    .on_click(move |_| {
                        self_clone.reset_view();
                    })
                    .style(|s| s.margin(8.0)),

                floem::views::button("Zoom In")
                    .on_click(move |_| {
                        let center_x = self_clone.canvas_width.get() / 2.0;
                        let center_y = self_clone.canvas_height.get() / 2.0;
                        self_clone.zoom(100.0, center_x, center_y);
                    })
                    .style(|s| s.margin(8.0)),

                floem::views::button("Zoom Out")
                    .on_click(move |_| {
                        let center_x = self_clone.canvas_width.get() / 2.0;
                        let center_y = self_clone.canvas_height.get() / 2.0;
                        self_clone.zoom(-100.0, center_x, center_y);
                    })
                    .style(|s| s.margin(8.0)),
            ))
            .style(|s| s.width_full().height(50.0).background(Color::rgb8(0x30, 0x30, 0x30))),

            // Canvas del grafo
            self.create_canvas(),
        ))
        .style(|s| s.size_full().background(Color::rgb8(0x21, 0x21, 0x21)))
    }


    /// Ottiene il nodo selezionato
    pub fn get_selected_node(&self) -> Option<String> {
        self.selected_node.get()
    }

    /// Ottiene il task associato al nodo selezionato
    pub fn get_selected_task(&self) -> Option<Task> {
        let node_id = self.selected_node.get()?;
        let graph = self.current_graph.get()?;

        let graph_guard = match graph.read() {
            Ok(g) => g,
            Err(_) => return None,
        };

        graph_guard.get_task(&node_id).cloned()
    }
    /// Crea la vista degli archi tra i nodi
    fn create_edge_view(&self, from: &NodeLayoutInfo, to: &NodeLayoutInfo) -> impl View {
        let scale = self.scale;
        let offset_x = self.offset_x;
        let offset_y = self.offset_y;
        let selected_node = self.selected_node;
        let highlighted_node = self.highlighted_node;

        let from_x = from.position.x * scale.get() + offset_x.get() + from.size.width / 2.0;
        let from_y = from.position.y * scale.get() + offset_y.get() + from.size.height;
        let to_x = to.position.x * scale.get() + offset_x.get() + to.size.width / 2.0;
        let to_y = to.position.y * scale.get() + offset_y.get();

        // Calcola i punti di controllo per la curva Bezier
        let control_y = (from_y + to_y) / 2.0;

        // Crea la path SVG
        let path = format!(
            "M {},{} C {},{} {},{} {},{}",
            from_x, from_y,
            from_x, control_y,
            to_x, control_y,
            to_x, to_y
        );

        svg(move || {
            let mut svg = floem::views::svg::Svg::new();

            let selected = selected_node.get().map(|id| id == from.id || id == to.id).unwrap_or(false);
            let highlighted = highlighted_node.get().map(|id| id == from.id || id == to.id).unwrap_or(false);

            // Stile dell'arco in base alla selezione
            let stroke_width = if selected { 2.5 } else if highlighted { 2.0 } else { 1.5 };
            let stroke_color = if selected {
                Color::rgb8(0xFF, 0xC1, 0x07) // Giallo per selezione
            } else if highlighted {
                Color::rgb8(0xFF, 0xE0, 0x82) // Giallo chiaro per hover
            } else {
                Color::rgb8(0x99, 0x99, 0x99) // Grigio per default
            };

            svg.add(
                floem::views::svg::Path::new()
                    .attr("d", path)
                    .attr("fill", "none")
                    .attr("stroke", format!("#{:02x}{:02x}{:02x}", 
                                         stroke_color.r(), 
                                         stroke_color.g(), 
                                         stroke_color.b()))
                    .attr("stroke-width", stroke_width.to_string())
            );

            // Aggiungi una freccia alla fine dell'arco
            let arrow_size = 6.0;
            let angle = (to_y - control_y).atan2(to_x - from_x);
            let arrow_x1 = to_x - arrow_size * (angle - 0.5).cos();
            let arrow_y1 = to_y - arrow_size * (angle - 0.5).sin();
            let arrow_x2 = to_x - arrow_size * (angle + 0.5).cos();
            let arrow_y2 = to_y - arrow_size * (angle + 0.5).sin();

            svg.add(
                floem::views::svg::Path::new()
                    .attr("d", format!("M {},{} L {},{} L {},{} Z", 
                                      to_x, to_y, 
                                      arrow_x1, arrow_y1, 
                                      arrow_x2, arrow_y2))
                    .attr("fill", format!("#{:02x}{:02x}{:02x}", 
                                       stroke_color.r(), 
                                       stroke_color.g(), 
                                       stroke_color.b()))
            );

            svg
        })
        .style(|s| s.position(floem::style::Position::Absolute).size_full())
    }

    /// Crea il canvas per disegnare il grafo
    fn create_canvas(&self) -> impl View {
        let node_layout = self.node_layout;
        let current_graph = self.current_graph.clone();
        let scale = self.scale;
        let offset_x = self.offset_x;
        let offset_y = self.offset_y;
        let selected_node = self.selected_node;
        let highlighted_node = self.highlighted_node;
        let canvas_width = self.canvas_width;
        let canvas_height = self.canvas_height;
        let self_clone = self.clone();

        // Memo per gli archi (edges) del grafo
        let edges = create_memo(move |_| {
            let graph = match current_graph.get() {
                Some(g) => g,
                None => return Vec::new(),
            };

            let graph_guard = match graph.read() {
                Ok(g) => g,
                Err(_) => return Vec::new(),
            };

            let layout = node_layout.get();
            let mut edges = Vec::new();

            // Crea gli archi in base alle dipendenze
            for (task_id, layout_info) in &layout {
                let task = match graph_guard.get_task(task_id) {
                    Some(t) => t,
                    None => continue,
                };

                for dep_id in &task.dependencies {
                    if let Some(dep_layout) = layout.get(dep_id) {
                        edges.push((dep_layout.clone(), layout_info.clone()));
                    }
                }
            }

            edges
        });

        // Container principale
        scroll(
            container(
                // Layer per gli archi
                container(
                    svg(move || {
                        let mut svg = floem::views::svg::Svg::new();
                        svg.set_viewbox(0.0, 0.0, canvas_width.get(), canvas_height.get());
                        svg
                    })
                    .style(|s| s.size_full())
                    .children_fn(move || {
                        edges.get().into_iter().map(|(from, to)| self_clone.create_edge_view(&from, &to).any()).collect()
                    })
                )
                .style(|s| s.size_full().z_index(1))

                // Layer per i nodi
                .child(
                    container(floem::views::empty())
                        .style(|s| s.size_full().z_index(2))
                        .children_fn(move || {
                            let layout = node_layout.get();
                            let selected_id = selected_node.get();
                            let highlighted_id = highlighted_node.get();

                            // Aggiorna lo stato di selezione e highlight
                            let mut updated_layout = layout.clone();
                            for (id, info) in &mut updated_layout {
                                info.selected = selected_id.as_ref().map_or(false, |sel_id| sel_id == id);
                                info.highlighted = highlighted_id.as_ref().map_or(false, |hl_id| hl_id == id);
                            }

                            // Ottieni il grafo
                            let graph = match current_graph.get() {
                                Some(g) => g,
                                None => return vec![],
                            };

                            let graph_guard = match graph.read() {
                                Ok(g) => g,
                                Err(_) => return vec![],
                            };

                            // Crea i nodi
                            updated_layout.iter()
                                .filter_map(|(id, layout_info)| {
                                    graph_guard.get_task(id).map(|task| {
                                        let node = self_clone.draw_node(task, layout_info);
                                        node.any()
                                    })
                                })
                                .collect()
                        })
                )
            )
            .style(|s| s.size_full().background(Color::rgb8(0x1E, 0x1E, 0x1E)))
            .on_event(FloemEvent::PointerWheel, move |event| {
                if let FloemEvent::PointerWheel(pointer_event) = event {
                    // Zoom con la rotella del mouse
                    let delta = pointer_event.delta_y;
                    let old_scale = scale.get();
                    let new_scale = (old_scale * (1.0 - delta * 0.001)).max(0.2).min(5.0);
                    scale.set(new_scale);

                    // Adatta l'offset per mantenere il punto sotto il cursore
                    let mouse_x = pointer_event.pos.x;
                    let mouse_y = pointer_event.pos.y;
                    let dx = mouse_x - offset_x.get();
                    let dy = mouse_y - offset_y.get();
                    let factor = new_scale / old_scale;

                    offset_x.set(mouse_x - dx * factor);
                    offset_y.set(mouse_y - dy * factor);

                    true
                } else {
                    false
                }
            })
            .on_event(FloemEvent::PointerDown, move |event| {
                if let FloemEvent::PointerDown(pointer_event) = event {
                    // Verifica se il click è sul background (non su un nodo)
                    if pointer_event.target == pointer_event.current {
                        // Inizia il panning
                        // Nota: qui dovresti implementare il tracking dello stato del panning
                        // e gestire gli eventi PointerMove e PointerUp per completare il panning
                        true
                    } else {
                        false
                    }
                } else {
                    false
                }
            })
        )
        .style(|s| s.size_full().background(Color::rgb8(0x1E, 0x1E, 0x1E)))
    }

    /// Funzione per disegnare un nodo
    fn draw_node(&self, task: &Task, layout: &NodeLayoutInfo) -> impl View {
        let id = task.id.clone();
        let state = task.state;
        let name = task.metadata.name.clone();
        let selected = layout.selected;
        let highlighted = layout.highlighted;
        let scale = self.scale;
        let offset_x = self.offset_x;
        let offset_y = self.offset_y;
        let selected_node = self.selected_node;
        let highlighted_node = self.highlighted_node;

        let task_type = task.metadata.task_type.clone();

        // Determina il colore in base allo stato
        let fill_color = match state {
            TaskState::Pending => Color::rgb8(0x41, 0x41, 0x41),    // Grigio scuro
            TaskState::Running => Color::rgb8(0x00, 0x87, 0xE8),    // Blu
            TaskState::Completed => Color::rgb8(0x28, 0xA7, 0x45),  // Verde
            TaskState::Failed => Color::rgb8(0xDC, 0x30, 0x30),     // Rosso
            TaskState::Cancelled => Color::rgb8(0x88, 0x88, 0x88),  // Grigio chiaro
        };

        // Stile del bordo in base alla selezione
        let border_width = if selected { 3.0 } else if highlighted { 2.0 } else { 1.0 };
        let border_color = if selected {
            Color::rgb8(0xFF, 0xC1, 0x07) // Giallo per selezione
        } else if highlighted {
            Color::rgb8(0xFF, 0xE0, 0x82) // Giallo chiaro per hover
        } else {
            Color::rgb8(0x66, 0x66, 0x66) // Grigio per default
        };

        // Contenitore del nodo con interazioni
        container(
            v_stack((
                label(move || name.clone())
                    .style(|s| s.color(Color::WHITE).font_size(12.0).margin(2.0)),
                label(move || task_type.clone())
                    .style(|s| s.color(Color::rgba8(255, 255, 255, 180)).font_size(10.0).margin(2.0)),
            ))
            .style(|s| s.align_items(floem::style::AlignItems::Center).justify_content(floem::style::JustifyContent::Center))
        )
        .style(move |s| {
            s.position(floem::style::Position::Absolute)
                .width(layout.size.width)
                .height(layout.size.height)
                .left(layout.position.x * scale.get() + offset_x.get())
                .top(layout.position.y * scale.get() + offset_y.get())
                .background(fill_color)
                .border(border_width)
                .border_color(border_color)
                .border_radius(Corner::all(8.0))
        })
        .on_event(FloemEvent::PointerDown, move |_| {
            selected_node.set(Some(id.clone()));
            true
        })
        .on_event(FloemEvent::PointerEnter, move |_| {
            highlighted_node.set(Some(id.clone()));
            true
        })
        .on_event(FloemEvent::PointerLeave, move |_| {
            highlighted_node.set(None);
            true
        })
    }

    /// Funzione per gestire lo zoom del grafo
    pub fn zoom(&self, factor: f64, center_x: f64, center_y: f64) {
        let old_scale = self.scale.get();
        let new_scale = (old_scale * factor).max(0.2).min(5.0);
        
        // Aggiorna la scala
        self.scale.set(new_scale);
        
        // Aggiorna l'offset per mantenere il centro dello zoom
        let dx = center_x - self.offset_x.get();
        let dy = center_y - self.offset_y.get();
        let scale_factor = new_scale / old_scale;
        
        self.offset_x.set(center_x - dx * scale_factor);
        self.offset_y.set(center_y - dy * scale_factor);
    }

    /// Funzione per gestire il pan (spostamento) del grafo
    pub fn pan(&self, delta_x: f64, delta_y: f64) {
        let current_x = self.offset_x.get();
        let current_y = self.offset_y.get();
        
        self.offset_x.set(current_x + delta_x);
        self.offset_y.set(current_y + delta_y);
    }

    /// Esporta il grafo come immagine
    pub fn export_to_image(&self, path: &str) -> Result<(), UiError> {
        // In una implementazione reale, qui si potrebbe:
        // 1. Catturare il contenuto del canvas
        // 2. Convertirlo in un'immagine
        // 3. Salvarlo su disco nel percorso specificato
        
        // Esempio di implementazione semplificata
        Err(UiError::OperationError("Funzionalità di esportazione immagine non ancora implementata".to_string()))
    }

    /// Reimposta la vista del grafo (zoom e posizione)
    pub fn reset_view(&self) {
        self.scale.set(1.0);
        self.offset_x.set(0.0);
        self.offset_y.set(0.0);
        
        // Ricalcola il layout
        let _ = self.update_layout();
    }
}

impl Clone for DagViewer {
    fn clone(&self) -> Self {
        Self {
            core: self.core.clone(),
            theme_manager: self.theme_manager.clone(),
            current_graph_id: self.current_graph_id,
            node_layout: self.node_layout,
            selected_node: self.selected_node,
            highlighted_node: self.highlighted_node,
            layout_engine: GraphLayoutEngine::new(),
            scale: self.scale,
            offset_x: self.offset_x,
            offset_y: self.offset_y,
            current_graph: self.current_graph.clone(),
            canvas_width: self.canvas_width,
            canvas_height: self.canvas_height,
        }
    }
}