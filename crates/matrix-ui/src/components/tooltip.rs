//! Componente tooltip
//!
//! Questo modulo fornisce un componente tooltip per l'IDE MATRIX.

use std::sync::Arc;
use floem::{View, views::{container, label}};
use floem::style::{BoxShadow};
use floem::peniko::Color;
use crate::theme::ThemeManager;

/// Posizione del tooltip
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TooltipPosition {
    /// Sopra l'elemento
    Top,

    /// Sotto l'elemento
    Bottom,

    /// A sinistra dell'elemento
    Left,

    /// A destra dell'elemento
    Right,
}

/// Configurazione del tooltip
pub struct TooltipConfig {
    /// Posizione del tooltip
    pub position: TooltipPosition,

    /// Ritardo prima della visualizzazione (in millisecondi)
    pub delay_ms: u64,

    /// Durata della visualizzazione (in millisecondi, 0 per infinito)
    pub duration_ms: u64,
}

impl Default for TooltipConfig {
    fn default() -> Self {
        Self {
            position: TooltipPosition::Bottom,
            delay_ms: 500,
            duration_ms: 0, // infinito
        }
    }
}

/// Aggiunge un tooltip a una vista
pub fn with_tooltip<V: View + 'static>(
    theme_manager: Arc<ThemeManager>,
    view: V,
    text: &str,
    config: Option<TooltipConfig>,
) -> impl View {
    let theme = theme_manager.get_active_theme().unwrap_or_default();
    let config = config.unwrap_or_default();
    let tooltip_text = text.to_string();

    // Crea il tooltip
    let tooltip = label(move || tooltip_text.clone())
        .style(move |s| {
            s.position(floem::style::Position::Absolute)
             .z_index(1000)
             .padding(8.0)
             .border_radius(theme.borders.radius_small)
             .background(theme.colors.background_tertiary)
             .color(theme.colors.text)
             .font_size(theme.font_sizes.xs)
             .box_shadow(BoxShadow {
                 color: Color::rgba8(0, 0, 0, 100),
                 offset: (0.0, 2.0),
                 blur_radius: 4.0,
                 spread_radius: 0.0,
             })
             // Posiziona il tooltip in base alla configurazione
             .apply(|s| match config.position {
                 TooltipPosition::Top => s.bottom(100.0).left(50.0).transform("translateX(-50%) translateY(-8px)"),
                 TooltipPosition::Bottom => s.top(100.0).left(50.0).transform("translateX(-50%) translateY(8px)"),
                 TooltipPosition::Left => s.right(100.0).top(50.0).transform("translateX(-8px) translateY(-50%)"),
                 TooltipPosition::Right => s.left(100.0).top(50.0).transform("translateX(8px) translateY(-50%)"),
             })
             // Inizialmente nascosto
             .display(floem::style::Display::None)
        });

    // Aggiungi il tooltip alla vista originale
    container(view)
        .style(|s| s.position(floem::style::Position::Relative))
        .on_hover_start(move |_| {
            // Mostra il tooltip dopo il ritardo specificato
            let delay_ms = config.delay_ms;
            if delay_ms > 0 {
                // Qui normalmente useremmo un timer per mostrare il tooltip dopo il ritardo
                // Ma Floem non ha un'API diretta per i timer, quindi questo è un mock
                // In un'implementazione reale, questo utilizzerebbe un timer di sistema
                println!("Tooltip would show after {} ms", delay_ms);
            }

            // Nascondi il tooltip dopo la durata specificata
            if config.duration_ms > 0 {
                // Anche qui, useremmo un timer in un'implementazione reale
                println!("Tooltip would hide after {} ms", config.duration_ms);
            }
        })
        .on_hover_end(move |_| {
            // Nascondi il tooltip
            println!("Tooltip would hide on hover end");
        })
        .child(tooltip)
}

/// Crea un tooltip informativo
pub fn info_tooltip<V: View + 'static>(
    theme_manager: Arc<ThemeManager>,
    view: V,
    text: &str,
) -> impl View {
    with_tooltip(
        theme_manager,
        view,
        text,
        Some(TooltipConfig {
            position: TooltipPosition::Top,
            delay_ms: 300,
            duration_ms: 0,
        }),
    )
}

/// Crea un tooltip di aiuto
pub fn help_tooltip<V: View + 'static>(
    theme_manager: Arc<ThemeManager>,
    view: V,
    text: &str,
) -> impl View {
    let theme = theme_manager.get_active_theme().unwrap_or_default();
    let container_view = container(view)
        .style(move |s| {
            s.cursor(floem::style::Cursor::Help)
             .hover(|s| s.color(theme.colors.accent))
        });

    with_tooltip(
        theme_manager,
        container_view,
        text,
        Some(TooltipConfig {
            position: TooltipPosition::Bottom,
            delay_ms: 200,
            duration_ms: 0,
        }),
    )
}
