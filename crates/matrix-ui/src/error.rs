//! Gestione degli errori per il UI Layer di MATRIX IDE
//!
//! Questo modulo definisce i tipi di errore specifici del UI Layer.

use thiserror::Error;
use std::io;
use matrix_core::CoreError;

/// Errori che possono verificarsi nel UI Layer
#[derive(Error, Debug)]
pub enum UiError {
    /// Errore durante l'inizializzazione del UI Layer
    #[error("Initialization error: {0}")]
    InitializationError(String),

    /// Errore nell'integrazione con Lapce
    #[error("Lapce integration error: {0}")]
    LapceIntegrationError(String),

    /// Errore nel layout
    #[error("Layout error: {0}")]
    LayoutError(String),

    /// Errore nei pannelli
    #[error("Panel error: {0}")]
    PanelError(String),

    /// Errore nei temi
    #[error("Theme error: {0}")]
    ThemeError(String),

    /// Errore nei plugin
    #[error("Plugin error: {0}")]
    PluginError(String),

    /// Errore di I/O
    #[error("I/O error: {0}")]
    IoError(String),

    /// Errore di serializzazione
    #[error("Serialization error: {0}")]
    SerializationError(String),

    /// Errore nel Core Engine
    #[error("Core engine error: {0}")]
    CoreError(#[from] CoreError),

    /// Errore nell'Event Bus
    #[error("Event bus error: {0}")]
    EventBusError(String),

    /// Errore nell'Editor
    #[error("Editor error: {0}")]
    EditorError(String),

    /// Errore di lock
    #[error("Lock error: {0}")]
    LockError(String),

    /// Errore generico
    #[error("Error: {0}")]
    GenericError(String),
}

impl From<String> for UiError {
    fn from(err: String) -> Self {
        UiError::GenericError(err)
    }
}

impl From<&str> for UiError {
    fn from(err: &str) -> Self {
        UiError::GenericError(err.to_string())
    }
}

impl From<std::io::Error> for UiError {
    fn from(err: std::io::Error) -> Self {
        UiError::IoError(err.to_string())
    }
}
