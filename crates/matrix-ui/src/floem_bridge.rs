//! Bridge per l\'integrazione di Floem 0.2
//!
//! Questo modulo implementa l\'adattatore per integrare Floem 0.2 con il sistema
//! di temi e l\'interfaccia utente di MATRIX IDE.

use crate::theme::{Theme, ThemeColors, ThemeSpacing, ThemeFontSizes, ThemeBorders, ThemeIcons};
use crate::error::UiError;
use floem::peniko::Color;
use floem::style::{Style, Background, BorderRadius, TextColor, Transition, BorderColor};
use floem::unit::DurationUnitExt;
use std::sync::{Arc, RwLock};

/// Bridge per la conversione dei temi di MATRIX IDE in stili Floem
pub struct FloemBridge {
    /// Tema corrente
    theme: Arc<RwLock<Theme>>,

    /// Stile principale dell\'applicazione
    app_style: Arc<RwLock<Style>>,

    /// Mappatura dei componenti ai loro stili
    component_styles: Arc<RwLock<std::collections::HashMap<String, Style>>>,

    /// ID univoco per il bridge
    id: uuid::Uuid,
}

/// Componenti principali dell'interfaccia
pub enum UiComponent {
    Editor,
    Button,
    Panel,
    Sidebar,
    StatusBar,
    Toolbar,
    Dialog,
    Input,
    Label,
    Tab,
    Tree,
    List,
    Menu,
    ContextMenu,
    Dropdown,
    Checkbox,
    RadioButton,
    Slider,
    Progress,
    ScrollView,
    Frame,
}

impl FloemBridge {
    /// Crea un nuovo bridge per Floem con il tema specificato
    pub fn new(theme: Theme) -> Self {
        let app_style = create_app_style(&theme);
        let component_styles = create_component_styles(&theme);

        Self {
            theme: Arc::new(RwLock::new(theme)),
            app_style: Arc::new(RwLock::new(app_style)),
            component_styles: Arc::new(RwLock::new(component_styles)),
            id: uuid::Uuid::new_v4(),
        }
    }

    /// Aggiorna il tema corrente
    pub fn update_theme(&self, theme: Theme) -> Result<(), UiError> {
        let mut theme_guard = self.theme.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on theme".to_string())
        })?;
        *theme_guard = theme.clone();
        drop(theme_guard);

        // Aggiorna gli stili in base al nuovo tema
        let app_style = create_app_style(&theme);
        let component_styles = create_component_styles(&theme);

        let mut app_style_guard = self.app_style.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on app style".to_string())
        })?;
        *app_style_guard = app_style;

        let mut component_styles_guard = self.component_styles.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on component styles".to_string())
        })?;
        *component_styles_guard = component_styles;

        Ok(())
    }

    /// Ottiene lo stile Floem per il componente specificato
    pub fn get_component_style(&self, component: UiComponent) -> Result<Style, UiError> {
        let component_name = match component {
            UiComponent::Editor => "editor",
            UiComponent::Button => "button",
            UiComponent::Panel => "panel",
            UiComponent::Sidebar => "sidebar",
            UiComponent::StatusBar => "status_bar",
            UiComponent::Toolbar => "toolbar",
            UiComponent::Dialog => "dialog",
            UiComponent::Input => "input",
            UiComponent::Label => "label",
            UiComponent::Tab => "tab",
            UiComponent::Tree => "tree",
            UiComponent::List => "list",
            UiComponent::Menu => "menu",
            UiComponent::ContextMenu => "context_menu",
            UiComponent::Dropdown => "dropdown",
            UiComponent::Checkbox => "checkbox",
            UiComponent::RadioButton => "radio_button",
            UiComponent::Slider => "slider",
            UiComponent::Progress => "progress",
            UiComponent::ScrollView => "scroll_view",
            UiComponent::Frame => "frame",
        };

        let component_styles = self.component_styles.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on component styles".to_string())
        })?;

        component_styles.get(component_name).cloned().ok_or_else(|| {
            UiError::ThemeError(format!("Style for component '{}' not found", component_name))
        })
    }

    /// Ottiene lo stile principale dell'applicazione
    pub fn get_app_style(&self) -> Result<Style, UiError> {
        let app_style = self.app_style.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on app style".to_string())
        })?;

        Ok((*app_style).clone())
    }

    /// Crea uno stile editor personalizzato basato sul tema corrente
    pub fn create_editor_style(&self) -> Result<floem::views::editor::EditorStyle, UiError> {
        let theme = self.theme.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on theme".to_string())
        })?;

        // Converti i colori del tema in colori per l'editor
        let editor_style = floem::views::editor::EditorStyle::new()
            .text_color(convert_color(&theme.colors.text))
            .background(convert_color(&theme.colors.background))
            .selection_color(convert_color(&theme.colors.accent).with_alpha_factor(0.3))
            .cursor_color(convert_color(&theme.colors.accent))
            .cursor_width(theme.borders.width_normal)
            .border_color(convert_color(&theme.colors.border))
            .gutter_background(convert_color(&theme.colors.background_secondary))
            .gutter_foreground(convert_color(&theme.colors.text_secondary));

        Ok(editor_style)
    }

    /// Crea una configurazione per la colorazione sintattica
    pub fn create_syntax_highlighting(&self) -> Result<floem::views::editor::text::SimpleStylingBuilder, UiError> {
        let theme = self.theme.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on theme".to_string())
        })?;

        let styling = floem::views::editor::text::SimpleStylingBuilder::default()
            .font_size(theme.font_sizes.md as usize)
            .font_family(vec![
                floem::text::FamilyOwned::Name("Fira Code".to_string()),
                floem::text::FamilyOwned::Name("Consolas".to_string()),
                floem::text::FamilyOwned::Monospace,
            ]);

        Ok(styling)
    }

    /// Registra classi di stile Floem
    pub fn register_style_classes(&self) -> Result<(), UiError> {
        // Implementazione per definire classi di stile comuni
        // Questo è solo un esempio di come potrebbe funzionare
        Ok(())
    }
}

/// Crea lo stile principale dell'applicazione in base al tema
fn create_app_style(theme: &Theme) -> Style {
    let transition_duration = 200.millis();

    Style::new()
        .background(convert_color(&theme.colors.background))
        .color(convert_color(&theme.colors.text))
        .font_size(theme.font_sizes.md)
        .transition(Background, Transition::ease_in_out(transition_duration))
        .transition(TextColor, Transition::ease_in_out(transition_duration))
}

/// Crea gli stili dei componenti in base al tema
fn create_component_styles(theme: &Theme) -> std::collections::HashMap<String, Style> {
    let mut styles = std::collections::HashMap::new();
    let transition_duration = 200.millis();

    // Stile per i pulsanti
    let button_style = Style::new()
        .background(convert_color(&theme.colors.background_secondary))
        .color(convert_color(&theme.colors.text))
        .border(theme.borders.width_normal)
        .border_color(convert_color(&theme.colors.border))
        .border_radius(theme.borders.radius_medium)
        .padding(theme.spacing.sm)
        .hover(|s| s.background(convert_color(&theme.colors.accent).with_alpha_factor(0.1)))
        .active(|s| s.background(convert_color(&theme.colors.accent).with_alpha_factor(0.2)))
        .focus_visible(|s| s.border_color(convert_color(&theme.colors.accent)))
        .transition(Background, Transition::ease_in_out(transition_duration))
        .transition(BorderColor, Transition::ease_in_out(transition_duration))
        .transition(TextColor, Transition::ease_in_out(transition_duration));

    // Stile per i pannelli
    let panel_style = Style::new()
        .background(convert_color(&theme.colors.background_secondary))
        .border(theme.borders.width_thin)
        .border_color(convert_color(&theme.colors.border))
        .border_radius(theme.borders.radius_medium)
        .padding(theme.spacing.md);

    // Stile per la barra laterale
    let sidebar_style = Style::new()
        .background(convert_color(&theme.colors.background_secondary))
        .border_right(theme.borders.width_thin)
        .border_color(convert_color(&theme.colors.border))
        .width(250.0); // Larghezza predefinita

    // Stile per l'editor
    let editor_style = Style::new()
        .background(convert_color(&theme.colors.background))
        .color(convert_color(&theme.colors.text))
        .font_size(theme.font_sizes.md)
        .size_full();

    // Stile per input di testo
    let input_style = Style::new()
        .background(convert_color(&theme.colors.background))
        .color(convert_color(&theme.colors.text))
        .border(theme.borders.width_normal)
        .border_color(convert_color(&theme.colors.border))
        .border_radius(theme.borders.radius_small)
        .padding(theme.spacing.xs)
        .focus_visible(|s| s.border_color(convert_color(&theme.colors.accent)))
        .transition(BorderColor, Transition::ease_in_out(transition_duration));

    // Stile per le etichette
    let label_style = Style::new()
        .color(convert_color(&theme.colors.text))
        .font_size(theme.font_sizes.md)
        .margin(theme.spacing.xxs);

    // Stile per frame contenitori
    let frame_style = Style::new()
        .background(convert_color(&theme.colors.background_tertiary).with_alpha_factor(0.1))
        .border(theme.borders.width_thin)
        .border_color(convert_color(&theme.colors.border).with_alpha_factor(0.2))
        .border_radius(theme.borders.radius_medium)
        .padding(theme.spacing.md);

    // Inserisci gli stili nella mappa
    styles.insert("button".to_string(), button_style);
    styles.insert("panel".to_string(), panel_style);
    styles.insert("sidebar".to_string(), sidebar_style);
    styles.insert("editor".to_string(), editor_style);
    styles.insert("input".to_string(), input_style);
    styles.insert("label".to_string(), label_style);
    styles.insert("frame".to_string(), frame_style);

    // Aggiungi altri stili qui...

    styles
}

/// Converte un colore Floem in formato Color di Floem
fn convert_color(color: &Color) -> floem::peniko::Color {
    let (r, g, b, a) = color.as_rgba8();
    floem::peniko::Color::from_rgba8(r, g, b, a)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::theme::Theme;

    #[test]
    fn test_bridge_creation() {
        let theme = Theme::default();
        let bridge = FloemBridge::new(theme);
        assert!(bridge.id != uuid::Uuid::nil());
    }
}
