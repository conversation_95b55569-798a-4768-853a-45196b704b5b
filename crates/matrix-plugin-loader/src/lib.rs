//! # Matrix Plugin Loader
//!
//! Un sistema per caricare e gestire plugin per MATRIX IDE.

mod auto_update;
mod dependency_resolver;
mod manifest;
mod registry;
mod semver;
mod version_compatibility;

pub use auto_update::{hot_reload_plugin, AutoUpdateError, AutoUpdateManager, RemotePluginStatus};
pub use dependency_resolver::{DependencyError, DependencyResolver, DependencyResult};
pub use manifest::{
    Dependency, Manifest, ManifestError, ManifestResult, Metadata, Permissions, Version,
    VersionRequirement,
};
pub use registry::{PluginRegistry, RegistryError, RegistryResult};
pub use crate::semver::{
    Sem<PERSON><PERSON>, SemVerError, SemVerResult, Comparator, VersionConstraint,
};
pub use version_compatibility::{
    version_to_semver, semver_to_version, requirement_to_constraint, constraint_to_requirement,
    VersionConversionError,
};

/// Versione del crate
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

