<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" fill-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="1.414" clip-rule="evenodd" viewBox="0 0 1200 800" width="1200" height="800">
  <path fill="#a52b00" fill-rule="nonzero" d="M597.344 357.461c-121.238 0-231.39 14.576-312.939 38.329v202.901c81.549 23.754 191.701 38.329 312.939 38.329 138.76 0 262.987-19.092 346.431-49.186V406.65c-83.444-30.095-207.671-49.189-346.431-49.189m471.406 164.861-14.211-29.441c.073-1.118.131-2.237.131-3.36 0-33.375-34.706-64.135-93.097-88.762v177.526c27.245-11.492 49.348-24.317 65.156-38.125-4.652 18.707-20.564 56.553-33.25 83.168-20.85 38.066-28.4 71.203-27.432 72.844.617 1.033 7.73-9.94 18.219-27.721 24.41-34.781 70.664-101.182 79.909-118.096 10.472-19.174 4.575-28.033 4.575-28.033m-919.686-30.855c0 6.428 1.293 12.76 3.788 18.969l-8.511 15.15s-6.818 8.846 5.188 27.508c10.59 16.455 63.635 80.867 91.632 114.598 12.026 17.293 20.179 27.945 20.893 26.916 1.124-1.633-7.476-34.1-31.359-71.141-11.144-19.895-24.454-46.252-32.442-65.721 22.303 14.116 51.483 26.92 86.152 37.94V387.251c-83.544 26.548-135.341 63.433-135.341 104.216"/>
  <path fill="url(#a)" fill-rule="nonzero" d="M.991-.034.933.008v.018L.99.069a.018.018 0 0 1 .008.018.022.022 0 0 1-.012.016L.92.133A.085.085 0 0 1 .916.15l.048.053C.968.208.97.216.968.222a.023.023 0 0 1-.015.014L.882.254.875.27.91.33a.018.018 0 0 1 0 .02C.907.356.9.36.893.361L.82.365.81.379l.022.066c.003.007.001.014-.004.02a.022.022 0 0 1-.019.007L.737.462a.154.154 0 0 1-.013.012l.009.07a.018.018 0 0 1-.008.018C.719.566.711.568.704.565L.636.542l-.015.01-.006.069a.02.02 0 0 1-.011.017.026.026 0 0 1-.021 0L.521.602.518.603.406.729S.394.747.359.725C.329.705.206.599.141.543A.267.267 0 0 1 .09.502C.093.499.149.509.217.554.278.588.371.631.38.619c0 0 .016-.015.026-.044.001.001.001.001 0 0 0 0-.315-.551-.101-1.106.006-.062-.03-.096-.03-.096C.266-.639.178-.598.12-.566c-.065.043-.118.053-.12.05a.245.245 0 0 1 .049-.039C.11-.608.227-.707.256-.726.289-.748.301-.73.301-.73l.101.115.013.002.055-.045c.005-.005.013-.006.02-.004a.02.02 0 0 1 .014.014l.018.068.016.006.064-.034a.02.02 0 0 1 .021 0c.006.003.01.009.01.016l.004.07.015.01.069-.022a.023.023 0 0 1 .02.003.02.02 0 0 1 .008.019l-.011.069a.154.154 0 0 0 .013.012l.072-.008a.023.023 0 0 1 .019.007.018.018 0 0 1 .003.02l-.024.065.01.015.072.005a.02.02 0 0 1 .017.011.018.018 0 0 1 0 .02l-.037.06.006.016.07.018a.021.021 0 0 1 .015.014.021.021 0 0 1-.005.02L.92-.116l.004.017.064.031A.02.02 0 0 1 1-.052c.001.007-.003.014-.009.018ZM.406.575Z" transform="scale(-755.0803 755.0803) rotate(-85 .164 .977)"/>
  <path fill-rule="nonzero" d="M450.328 650.959c-1.664-1.42-2.536-2.262-2.536-2.262l142.542-11.677c-116.273-153.391-209.424-31.198-219.339-17.536v31.475h79.333Zm296.792-1.652c1.663-1.42 2.536-2.264 2.536-2.264L612.672 637.02C729 477.333 816.541 604.168 826.455 617.83v31.477H747.12Z"/>
  <path fill="url(#b)" fill-rule="nonzero" d="M1-.586s-.232.058-.476.421L.5-.064s.6.329-.076.795c0 0 .084-.145-.019-.534 0 0-.274.179-.265.539 0 0-.415-.345.184-.871 0 0 .215-.556.676-.601v.15Z" transform="matrix(0 -267.211 -267.211 0 809.65 764.223)"/>
  <path fill-rule="nonzero" d="M677.392 417.547s43.486-47.615 86.974 0c0 0 34.17 63.492 0 95.234 0 0-55.912 44.444-86.974 0 0 0-37.275-34.921 0-95.234"/>
  <path fill="#fff" fill-rule="nonzero" d="M727.738 435.211c0 18.541-10.93 33.572-24.408 33.572-13.477 0-24.406-15.031-24.406-33.572 0-18.541 10.929-33.574 24.406-33.574 13.478 0 24.408 15.033 24.408 33.574"/>
  <path fill-rule="nonzero" d="M483.3 404.545s74.596-33.028 94.956 40.691c0 0 21.327 85.926-61.259 90.776 0 0-105.31-20.283-33.697-131.467"/>
  <path fill="#fff" fill-rule="nonzero" d="M520.766 436.428c0 19.119-11.27 34.627-25.173 34.627-13.898 0-25.171-15.508-25.171-34.627 0-19.124 11.273-34.627 25.171-34.627 13.903 0 25.173 15.503 25.173 34.627"/>
  <path fill="url(#c)" fill-rule="nonzero" d="M.367.129c-.731-.57-.144-.84-.144-.84.036.32.249.547.249.547C.521-.548.525-.77.525-.77c.678.514.064.931.064.931.038.104.183.211.317.29L1 .77C.376.403.367.129.367.129Z" transform="scale(-239.021 239.021) rotate(-90 .78 2.463)"/>
  <defs>
    <linearGradient id="a" x1="0" x2="1" y1="0" y2="0" gradientTransform="scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#f74c00"/>
      <stop offset=".33" stop-color="#f74c00"/>
      <stop offset="1" stop-color="#f49600"/>
    </linearGradient>
    <linearGradient id="b" x1="0" x2="1" y1="0" y2="0" gradientTransform="scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cc3a00"/>
      <stop offset=".15" stop-color="#cc3a00"/>
      <stop offset=".74" stop-color="#f74c00"/>
      <stop offset="1" stop-color="#f74c00"/>
    </linearGradient>
    <linearGradient id="c" x1="0" x2="1" y1="0" y2="0" gradientTransform="scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cc3a00"/>
      <stop offset=".15" stop-color="#cc3a00"/>
      <stop offset=".74" stop-color="#f74c00"/>
      <stop offset="1" stop-color="#f74c00"/>
    </linearGradient>
  </defs>
</svg>
