[package]
name = "floem_renderer"
version.workspace = true
edition = "2021"
repository = "https://github.com/lapce/floem"
description = "A native Rust UI library with fine-grained reactivity"
license.workspace = true

[dependencies]
parking_lot = { workspace = true }
peniko = { workspace = true }
image = { workspace = true }
resvg = { workspace = true }
swash = { workspace = true }

cosmic-text = { version = "0.12.1", features = ["shape-run-cache"] }
floem-winit = { version = "0.29.5", features = ["rwh_05"] }

wgpu = { workspace = true }
crossbeam = { version = "0.8" }
futures = "0.3.26"

[target.'cfg(target_arch = "wasm32")'.dependencies]
wasm-bindgen-futures = { version = "0.4" }
