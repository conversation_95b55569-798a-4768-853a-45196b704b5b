name: Deploy documentation

on:
  push:
    branches: ["main"]
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Pages
        id: pages
        uses: actions/configure-pages@v4

      - name: Update toolchain
        run: rustup install nightly

      - name: Build docs
        env:
          RUSTDOCFLAGS: "-Z unstable-options --enable-index-page"
        run: |
            cargo +nightly doc --no-deps --workspace --lib --release -Z unstable-options -Z rustdoc-scrape-examples
            chmod -c -R +rX "target/doc" | while read line; do
              echo "::warning title=Invalid file permissions automatically fixed::$line"
            done

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./target/doc

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4

