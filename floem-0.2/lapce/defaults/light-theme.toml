#:schema ../extra/schemas/color-theme.json

[color-theme]
name = "Lapce Light"

[ui]
font-family = ""
font-size = 13
header-height = 35
status-height = 25
tab-min-width = 100
activity-width = 50
scroll-width = 10
drop-shadow-width = 0

[color-theme.base]
black = "#383A42"
blue = "#4078F2"
cyan = "#0184BC"
green = "#50A14F"
grey = "#E5E5E6"
magenta = "#A626A4"
orange = "#D19A66"
purple = "#A626A4"
red = "#E45649"
white = "#FAFAFA"
yellow = "#C18401"

primary-background = "$white"
# Background for 'secondary' elements: panels, palette, status bar, completion, hover
secondary-background = "#EAEAEB"
current-background = "#DBDBDC"
text = "$black"
dim-text = "#A0A1A7"

[color-theme.syntax]
"comment" = "$dim-text"
"constant" = "$yellow"
"type" = "$yellow"
"typeAlias" = "$yellow"
"number" = "$yellow"
"enum" = "$yellow"
"struct" = "$yellow"
"structure" = "$yellow"
"interface" = "$yellow"
"attribute" = "$yellow"
"constructor" = "$yellow"

"function" = "$blue"
"method" = "$blue"
"function.method" = "$blue"

"keyword" = "$purple"
"selfKeyword" = "$purple"

"field" = "$red"
"property" = "$red"
"enumMember" = "$red"
"enum-member" = "$red"

"string" = "$green"
"string.escape" = "$cyan"

"type.builtin" = "$cyan"
"builtinType" = "$cyan"
"escape" = "$cyan"
"embedded" = "$cyan"

"punctuation.delimiter" = "$yellow"
"text.title" = "$orange"
"text.uri" = "$cyan"
"text.reference" = "$yellow"
"variable" = "$red"
"variable.other.member" = "$red"
"tag" = "$blue"

"markup.heading" = "$red"
"markup.bold" = "$orange"
"markup.italic" = "$orange"
"markup.list" = "$orange"
"markup.link.url" = "$blue"
"markup.link.label" = "$purple"
"markup.link.text" = "$purple"

"bracket.color.1" = "$blue"
"bracket.color.2" = "$yellow"
"bracket.color.3" = "$purple"
"bracket.unpaired" = "$red"

[color-theme.ui]
"lapce.error" = "#E51400"
"lapce.warn" = "#E9A700"
"lapce.dropdown_shadow" = "#B4B4B4"
"lapce.border" = "#B4B4B4"
"lapce.scroll_bar" = "#B4B4B4BB"

"lapce.button.primary.background" = "#50a14f"
"lapce.button.primary.foreground" = "$white"

# tab
"lapce.tab.active.background" = "$primary-background"
"lapce.tab.active.foreground" = "$text"
"lapce.tab.active.underline" = "#528BFF"

"lapce.tab.inactive.background" = "#EAEAEB"
"lapce.tab.inactive.foreground" = "$text"
"lapce.tab.inactive.underline" = "#528BFF77"

"lapce.tab.separator" = "#B4B4B4"

"lapce.icon.active" = "$text"
"lapce.icon.inactive" = "$dim-text"

"lapce.remote.icon" = "$white"
"lapce.remote.local" = "#4078F2"
"lapce.remote.connected" = "#50A14F"
"lapce.remote.connecting" = "#C18401"
"lapce.remote.disconnected" = "#E45649"

"lapce.plugin.name" = "#444444"
"lapce.plugin.description" = "$text"
"lapce.plugin.author" = "#707070"

"terminal.cursor" = "$text"
"terminal.foreground" = "$text"
"terminal.background" = "$primary-background"
"terminal.white" = "$white"
"terminal.black" = "$black"
"terminal.red" = "$red"
"terminal.blue" = "$blue"
"terminal.green" = "$green"
"terminal.yellow" = "$yellow"
"terminal.cyan" = "$cyan"
"terminal.magenta" = "$magenta"
"terminal.bright_white" = "#090A0B"
"terminal.bright_red" = "$red"
"terminal.bright_blue" = "$blue"
"terminal.bright_green" = "$green"
"terminal.bright_yellow" = "$yellow"
"terminal.bright_cyan" = "$cyan"
"terminal.bright_magenta" = "$magenta"
"terminal.bright_black" = "#A0A1A7"

"editor.background" = "$primary-background"
"editor.foreground" = "$text"
"editor.dim" = "$dim-text"
"editor.focus" = "#000000"
"editor.caret" = "#526FFF"
"editor.selection" = "$grey"
"editor.current_line" = "#F2F2F2"
"editor.debug_break_line" = "#528bFF55"
"editor.link" = "$blue"
"editor.visible_whitespace" = "$grey"
"editor.indent_guide" = "$grey"
"editor.drag_drop_background" = "#79c1fc33"
"editor.drag_drop_tab_background" = "#0b0e1433"
"editor.sticky_header_background" = "$primary-background"

"inlay_hint.foreground" = "$text"
"inlay_hint.background" = "#528bFF55"

"error_lens.error.foreground" = "$red"
"error_lens.error.background" = "#E4564920"
"error_lens.warning.foreground" = "$yellow"
"error_lens.warning.background" = "#C1840120"
"error_lens.other.foreground" = "$dim-text"
"error_lens.other.background" = "#A0A1A720"

"completion_lens.foreground" = "$dim-text"

"source_control.added" = "#50A14FCC"
"source_control.removed" = "#FF5266CC"
"source_control.modified" = "#0184BCCC"

"tooltip.background" = "$primary-background"
"tooltip.foreground" = "$text"

"palette.background" = "$secondary-background"
"palette.foreground" = "$text"
"palette.current.background" = "$current-background"
"palette.current.foreground" = "$text"

"completion.background" = "$secondary-background"
"completion.current" = "$current-background"

"hover.background" = "$secondary-background"

"activity.background" = "$secondary-background"
"activity.current" = "$primary-background"

"debug.breakpoint" = "$red"
"debug.breakpoint.hover" = "#E4564966"

"panel.background" = "$secondary-background"
"panel.foreground" = "$text"
"panel.foreground.dim" = "$dim-text"
"panel.current.background" = "$current-background"
"panel.current.foreground" = "$text"
"panel.current.foreground.dim" = "$dim-text"
"panel.hovered.background" = "#CBCBCB"
"panel.hovered.active.background" = "$dim-text"
"panel.hovered.foreground" = "$text"
"panel.hovered.foreground.dim" = "$dim-text"

"status.background" = "$secondary-background"
"status.foreground" = "$text"
"status.modal.normal.background" = "$blue"
"status.modal.normal.foreground" = "$white"
"status.modal.insert.background" = "$red"
"status.modal.insert.foreground" = "$white"
"status.modal.visual.background" = "$yellow"
"status.modal.visual.foreground" = "$white"
"status.modal.terminal.background" = "$purple"
"status.modal.terminal.foreground" = "$white"

"markdown.blockquote" = "#686868"
