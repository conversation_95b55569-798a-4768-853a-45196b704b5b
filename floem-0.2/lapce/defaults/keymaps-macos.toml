# --------------------------------- Window ---------------------------------------------

[[keymaps]]
command = "window_close"
key = "Meta+Shift+W"

# --------------------------------- General --------------------------------------------

[[keymaps]]
key = "meta+p"
command = "palette"

[[keymaps]]
key = "meta+shift+p"
command = "palette.command"

[[keymaps]]
key = "meta+e"
command = "toggle_code_glance"
mode = "i"

[[keymaps]]
key = "meta+,"
command = "open_settings"

[[keymaps]]
key = "meta+k meta+s"
command = "open_keyboard_shortcuts"

# [[keymaps]]
# key = "meta+q"
# command = "quit"

[[keymaps]]
key = "meta+="
command = "zoom_in"

[[keymaps]]
key = "meta+-"
command = "zoom_out"

[[keymaps]]
key = "meta+enter"
command = "source_control_commit"
when = "source_control_focus"

# --------------------------------- Basic editing ---------------------------------------

[[keymaps]]
key = "meta+z"
command = "undo"

[[keymaps]]
key = "meta+shift+z"
command = "redo"

[[keymaps]]
key = "meta+y"
command = "redo"

[[keymaps]]
key = "meta+x"
command = "clipboard_cut"

[[keymaps]]
key = "meta+c"
command = "clipboard_copy"

[[keymaps]]
key = "meta+v"
command = "clipboard_paste"

[[keymaps]]
key = "meta+f"
command = "search"

[[keymaps]]
key = "alt+right"
command = "word_end_forward"
mode = "i"

[[keymaps]]
key = "alt+left"
command = "word_backward"
mode = "i"

[[keymaps]]
key = "meta+left"
command = "line_start_non_blank"
mode = "i"

[[keymaps]]
key = "meta+right"
command = "line_end"
mode = "i"

[[keymaps]]
key = "ctrl+a"
command = "line_start_non_blank"
mode = "i"

[[keymaps]]
key = "ctrl+e"
command = "line_end"
mode = "i"

[[keymaps]]
key = "meta+shift+k"
command = "delete_line"
mode = "i"

[[keymaps]]
key = "alt+backspace"
command = "delete_word_backward"
mode = "i"

[[keymaps]]
key = "meta+backspace"
command = "delete_to_beginning_of_line"
mode = "i"

[[keymaps]]
key = "ctrl+k"
command = "delete_to_end_of_line"
mode = "i"

[[keymaps]]
key = "alt+delete"
command = "delete_word_forward"
mode = "i"

[[keymaps]]
key = "meta+shift+\\"
command = "match_pairs"
mode = "i"

[[keymaps]]
key = "meta+/"
command = "toggle_line_comment"

[[keymaps]]
key = "meta+]"
command = "indent_line"

[[keymaps]]
key = "meta+["
command = "outdent_line"

[[keymaps]]
key = "meta+a"
command = "select_all"

[[keymaps]]
key = "meta+enter"
command = "new_line_below"
when = "!source_control_focus"
mode = "i"

[[keymaps]]
key = "meta+shift+enter"
command = "new_line_above"
mode = "i"

# ------------------------------------ Multi cursor -------------------------------------

[[keymaps]]
key = "alt+meta+up"
command = "insert_cursor_above"
mode = "i"

[[keymaps]]
key = "alt+meta+down"
command = "insert_cursor_below"
mode = "i"

[[keymaps]]
key = "meta+l"
command = "select_current_line"
mode = "i"

[[keymaps]]
key = "meta+shift+l"
command = "select_all_current"
mode = "i"

[[keymaps]]
key = "meta+u"
command = "select_undo"
mode = "i"

[[keymaps]]
key = "meta+d"
command = "select_next_current"
mode = "i"

[[keymaps]]
key = "meta+k meta+d"
command = "select_skip_current"
mode = "i"

# ------------------------------------ File Management --------------------------------

[[keymaps]]
key = "meta+s"
command = "save"

[[keymaps]]
key = "meta+o"
command = "open_file"

[[keymaps]]
key = "meta+n"
command = "new_file"

# ----------------------------------- Editor Management -------------------------------

[[keymaps]]
key = "meta+w"
command = "split_close"

[[keymaps]]
key = "meta+k f"
command = "close_folder"

[[keymaps]]
key = "meta+\\"
command = "split_vertical"

# --------------------------------- Rich Language Editing ----------------------------

[[keymaps]]
key = "ctrl+space"
command = "get_completion"
mode = "i"

[[keymaps]]
key = "meta+i"
command = "get_completion"
mode = "i"

[[keymaps]]
key = "ctrl+shift+space"
command = "get_signature"
mode = "i"

[[keymaps]]
key = "meta+."
command = "show_code_actions"

# --------------------------------- Display -------------------------------------------

[[keymaps]]
key = "meta+shift+e"
command = "toggle_file_explorer_focus"

[[keymaps]]
key = "meta+shift+f"
command = "toggle_search_focus"

[[keymaps]]
key = "meta+shift+x"
command = "toggle_plugin_focus"

[[keymaps]]
key = "meta+shift+m"
command = "toggle_problem_focus"

# ------------------------------------ Navigation -------------------------------------

[[keymaps]]
key = "meta+up"
command = "document_start"

[[keymaps]]
key = "meta+down"
command = "document_end"

[[keymaps]]
key = "ctrl+f"
command = "right"
mode = "i"

[[keymaps]]
key = "ctrl+p"
command = "up"
when = "!list_focus"
mode = "i"

[[keymaps]]
key = "ctrl+n"
command = "down"
when = "!list_focus"
mode = "i"

[[keymaps]]
key = "meta+shift+o"
command = "palette.symbol"

[[keymaps]]
key = "meta+t"
command = "palette.workspace_symbol"

[[keymaps]]
key = "ctrl+g"
command = "palette.line"
