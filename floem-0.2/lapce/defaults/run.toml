
# The run config is used for both run mode and debug mode

[[configs]]
# the name of this task
name = "task"

# the type of the debugger. If not set, it can't be debugged but can still be run
# type = "lldb"

# the program to run, e.g. "${workspace}\\target\\debug\\check.exe"
program = ""

# the program arguments, e.g. args = ["arg1", "arg2"], optional
# args = []

# current working directory, optional
# cwd = "${workspace}"

# environment variables, optional
# [configs.env]
# VAR1 = "VAL1"
# VAR2 = "VAL2"

# task to run before the run/debug session is started, optional
# [configs.prelaunch]
# program = "cargo"
# args = [
#   "build",
# ]
