# --------------------------------- Window ---------------------------------------------

[[keymaps]]
command = "close_window"
key = "Alt+F4"

# --------------------------------- General --------------------------------------------

[[keymaps]]
key = "ctrl+p"
command = "palette"

[[keymaps]]
key = "ctrl+shift+p"
command = "palette.command"

[[keymaps]]
key = "ctrl+e"
command = "toggle_code_glance"
mode = "i"

[[keymaps]]
key = "ctrl+,"
command = "open_settings"

[[keymaps]]
key = "ctrl+k ctrl+s"
command = "open_keyboard_shortcuts"

[[keymaps]]
key = "ctrl+="
command = "zoom_in"

[[keymaps]]
key = "ctrl+-"
command = "zoom_out"

[[keymaps]]
key = "ctrl+enter"
command = "source_control_commit"
when = "source_control_focus"

# --------------------------------- Terminal copy/paste ---------------------------------

[[keymaps]]
key = "ctrl+shift+c"
command = "clipboard_copy"
mode = "t"

[[keymaps]]
key = "ctrl+shift+v"
command = "clipboard_paste"
mode = "t"

# --------------------------------- Basic editing ---------------------------------------

[[keymaps]]
key = "ctrl+z"
command = "undo"
mode = "i"

[[keymaps]]
key = "ctrl+shift+z"
command = "redo"
mode = "i"


[[keymaps]]
key = "ctrl+y"
command = "redo"
mode = "i"

[[keymaps]]
key = "ctrl+x"
command = "clipboard_cut"
mode = "i"

[[keymaps]]
key = "shift+Delete"
command = "clipboard_cut"
mode = "i"

[[keymaps]]
key = "ctrl+c"
command = "clipboard_copy"
mode = "i"

[[keymaps]]
key = "ctrl+insert"
command = "clipboard_copy"
mode = "i"

[[keymaps]]
key = "ctrl+v"
command = "clipboard_paste"
mode = "i"

[[keymaps]]
key = "shift+insert"
command = "clipboard_paste"
mode = "i"

[[keymaps]]
key = "ctrl+f"
command = "search"

[[keymaps]]
key = "ctrl+right"
command = "word_end_forward"
mode = "i"

[[keymaps]]
key = "ctrl+left"
command = "word_backward"
mode = "i"

[[keymaps]]
key = "ctrl+backspace"
command = "delete_word_backward"
mode = "i"

[[keymaps]]
key = "ctrl+delete"
command = "delete_word_forward"
mode = "i"

[[keymaps]]
key = "ctrl+shift+\\"
command = "match_pairs"
mode = "i"

[[keymaps]]
key = "ctrl+/"
command = "toggle_line_comment"

[[keymaps]]
key = "ctrl+]"
command = "indent_line"

[[keymaps]]
key = "ctrl+["
command = "outdent_line"

[[keymaps]]
key = "ctrl+a"
command = "select_all"

[[keymaps]]
key = "ctrl+enter"
command = "new_line_below"
when = "!source_control_focus"
mode = "i"

[[keymaps]]
key = "ctrl+shift+enter"
command = "new_line_above"
mode = "i"

# ------------------------------------ Multi cursor -------------------------------------

[[keymaps]]
key = "alt+ctrl+up"
command = "insert_cursor_above"
mode = "i"

[[keymaps]]
key = "alt+ctrl+down"
command = "insert_cursor_below"
mode = "i"

[[keymaps]]
key = "ctrl+l"
command = "select_current_line"
mode = "i"

[[keymaps]]
key = "ctrl+shift+l"
command = "select_all_current"
mode = "i"

[[keymaps]]
key = "ctrl+u"
command = "select_undo"
mode = "i"

[[keymaps]]
key = "ctrl+d"
command = "select_next_current"
mode = "i"

[[keymaps]]
key = "ctrl+k ctrl+d"
command = "select_skip_current"
mode = "i"

# ------------------------------------ File Management --------------------------------

[[keymaps]]
key = "ctrl+s"
command = "save"

[[keymaps]]
key = "ctrl+o"
command = "open_file"

[[keymaps]]
key = "ctrl+n"
command = "new_file"

# ----------------------------------- Editor Management -------------------------------

[[keymaps]]
key = "ctrl+w"
command = "split_close"
mode = "i"

[[keymaps]]
key = "ctrl+k f"
command = "close_folder"

[[keymaps]]
key = "ctrl+F4"
command = "split_close"

[[keymaps]]
key = "ctrl+\\"
command = "split_vertical"

# --------------------------------- Rich Language Editing ----------------------------

[[keymaps]]
key = "ctrl+space"
command = "get_completion"
mode = "i"

[[keymaps]]
key = "ctrl+i"
command = "get_completion"
mode = "i"

[[keymaps]]
key = "ctrl+shift+space"
command = "get_signature"
mode = "i"

[[keymaps]]
key = "ctrl+."
command = "show_code_actions"

# --------------------------------- Display -------------------------------------------

[[keymaps]]
key = "ctrl+shift+e"
command = "toggle_file_explorer_focus"

[[keymaps]]
key = "ctrl+shift+f"
command = "toggle_search_focus"

[[keymaps]]
key = "ctrl+shift+x"
command = "toggle_plugin_focus"

[[keymaps]]
key = "ctrl+shift+m"
command = "toggle_problem_focus"

# ------------------------------------ Navigation -------------------------------------

[[keymaps]]
key = "Ctrl+Home"
command = "document_start"

[[keymaps]]
key = "Ctrl+End"
command = "document_end"

[[keymaps]]
key = "ctrl+b"
command = "left"
mode = "n"

[[keymaps]]
key = "ctrl+f"
command = "right"
mode = "n"

[[keymaps]]
key = "ctrl+p"
command = "up"
when = "!list_focus"
mode = "n"

[[keymaps]]
key = "ctrl+n"
command = "down"
when = "!list_focus"
mode = "n"

[[keymaps]]
key = "ctrl+shift+o"
command = "palette.symbol"

[[keymaps]]
key = "ctrl+t"
command = "palette.workspace_symbol"

[[keymaps]]
key = "ctrl+g"
command = "palette.line"
