#:schema ../extra/schemas/settings.json

[core]
modal = false
color-theme = "Lapce Dark"
icon-theme = "Lapce Codicons"
custom-titlebar = true
file-explorer-double-click = false
auto-reload-plugin = false

[editor]
font-family = "monospace"
font-size = 13
code-glance-font-size = 2
line-height = 1.5
smart-tab = true
tab-width = 4
show-tab = true
show-bread-crumbs = true
scroll-beyond-last-line = true
cursor-surrounding-lines = 1
wrap-style = "editor-width"
wrap-column = 80
wrap-width = 600                                             # px
sticky-header = true
completion-width = 600
completion-show-documentation = true
completion-item-show-detail = false
show-signature = true
signature-label-code-block = true
auto-closing-matching-pairs = true
auto-surround = true
hover-delay = 300                                            # ms
modal-mode-relative-line-numbers = true
format-on-save = false
highlight-matching-brackets = true
highlight-selection-occurrences = true
highlight-scope-lines = false
autosave-interval = 0
format-on-autosave = true
normalize-line-endings = true
enable-inlay-hints = true
inlay-hint-font-family = ""
inlay-hint-font-size = 0
enable-error-lens = true
only-render-error-styling = true
error-lens-end-of-line = true
error-lens-font-family = ""
error-lens-font-size = 0
error-lens-multiline = false
enable-completion-lens = false
enable-inline-completion = true
completion-lens-font-family = ""
completion-lens-font-size = 0
blink-interval = 500                                         # ms
multicursor-case-sensitive = true
multicursor-whole-words = true
render-whitespace = "none"
show-indent-guide = true
atomic-soft-tabs = false
double-click = "single"
move-focus-while-search = true
diff-context-lines = 3
scroll-speed-modifier = 1
bracket-pair-colorization = false
bracket-colorization-limit = 30000
files-exclude = "**/{.git,.svn,.hg,CVS,.DS_Store,Thumbs.db}" # Glob patterns

[terminal]
font-family = ""
font-size = 0
line-height = 0

[terminal.default-profile]
macos = "default"
linux = "default"
windows = "default"

[terminal.profiles]
default = {}

# [terminal.profiles.example]
# command     = "cargo"
# arguments   = ["run"]
# environemnt = { "KEY" = "VALUE" }
# workdir     = "/home/<USER>"

[ui]
scale = +1.0
font-family = ""
font-size = 13
icon-size = 0
header-height = 36
status-height = 25
tab-min-width = 100
tab-separator-height = "Content"
scroll-width = 10
drop-shadow-width = 0
palette-width = 500
hover-font-family = ""
hover-font-size = 0
trim-search-results-whitespace = true
list-line-height = 25
tab-close-button = "Right"
open-editors-visible = true
