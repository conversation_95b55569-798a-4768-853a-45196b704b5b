# --------------------------------- General --------------------------------------------

[[keymaps]]
key = "F1"
command = "palette.command"

# [[keymaps]]
# key = "ctrl+q"
# command = "quit"

# --------------------------------- Basic editing ---------------------------------------

[[keymaps]]
key = "alt+up"
command = "move_line_up"
mode = "i"

[[keymaps]]
key = "alt+down"
command = "move_line_down"
mode = "i"

[[keymaps]]
key = "Delete"
command = "delete_forward"
mode = "i"

[[keymaps]]
key = "backspace"
command = "delete_backward"
mode = "i"

[[keymaps]]
key = "shift+backspace"
command = "delete_forward"
mode = "i"

[[keymaps]]
key = "Home"
command = "line_start_non_blank"
mode = "inv"

[[keymaps]]
key = "End"
command = "line_end"
mode = "inv"

[[keymaps]]
key = "PageUp"
command = "page_up"

[[keymaps]]
key = "PageDown"
command = "page_down"

[[keymaps]]
key = "Ctrl+PageUp"
command = "scroll_up"

[[keymaps]]
key = "Ctrl+PageDown"
command = "scroll_down"

# ------------------------------------ Multi cursor -------------------------------------

[[keymaps]]
key = "alt+shift+i"
command = "insert_cursor_end_of_line"
mode = "i"

# ----------------------------------- Editor Management -------------------------------

[[keymaps]]
key = "ctrl+tab"
command = "next_editor_tab"

[[keymaps]]
key = "ctrl+shift+tab"
command = "previous_editor_tab"

# --------------------------------- Rich Language Editing ----------------------------

[[keymaps]]
key = "F2"
command = "rename_symbol"

[[keymaps]]
key = "F12"
command = "goto_definition"

[[keymaps]]
key = "g f"
command = "show_code_actions"
mode = "n"

# ------------------------------------ Navigation -------------------------------------

[[keymaps]]
key = "F8"
command = "next_error"

[[keymaps]]
key = "shift+F8"
command = "previous_error"

[[keymaps]]
key = "ctrl+-"
command = "jump_location_backward"
mode = "i"

[[keymaps]]
key = "ctrl+shift+-"
command = "jump_location_forward"
mode = "i"

# --------------------------------- Integrated Terminal ------------------------------

[[keymaps]]
key = "Ctrl+`"
command = "toggle_terminal_focus"

# ------------------------------------ ------------ -------------------------------------

[[keymaps]]
key = "tab"
command = "jump_to_next_snippet_placeholder"
when = "in_snippet"
mode = "i"

[[keymaps]]
key = "shift+tab"
command = "jump_to_prev_snippet_placeholder"
when = "in_snippet"
mode = "i"

[[keymaps]]
key = "esc"
command = "clear_search"
when = "search_active || search_focus"

[[keymaps]]
key = "ctrl+shift+up"
command = "select_next_syntax_item"

[[keymaps]]
key = "ctrl+shift+down"
command = "select_previous_syntax_item"

[[keymaps]]
key = "ctrl+m"
command = "list.select"
when = "list_focus"

[[keymaps]]
key = "tab"
command = "list.select"
when = "completion_focus"

[[keymaps]]
key = "enter"
command = "list.select"
when = "list_focus"

[[keymaps]]
key = "ctrl+p"
command = "list.previous"
when = "list_focus"

[[keymaps]]
key = "up"
command = "list.previous"
when = "list_focus"

[[keymaps]]
key = "PageUp"
command = "list.previous_page"
when = "list_focus"

[[keymaps]]
key = "ctrl+n"
command = "list.next"
when = "list_focus"

[[keymaps]]
key = "down"
command = "list.next"
when = "list_focus"

[[keymaps]]
key = "PageDown"
command = "list.next_page"
when = "list_focus"

[[keymaps]]
key = "o"
command = "list.expand"
when = "list_focus"
mode = "n"

[[keymaps]]
key = "/"
command = "palette.line"
mode = "n"

[[keymaps]]
key = "esc"
command = "modal.close"
when = "modal_focus || completion_focus"

[[keymaps]]
key = "tab"
command = "inline_completion.select"
when = "inline_completion_visible && !search_focus && !modal_focus && !list_focus && !search_active"
mode = "i"

[[keymaps]]
key = "esc"
command = "inline_completion.cancel"
when = "inline_completion_visible && !search_focus && !modal_focus && !list_focus && !search_active"
mode = "i"

[[keymaps]]
key = "alt+["
command = "inline_completion.previous"
when = "inline_completion_visible && !search_focus && !modal_focus && !list_focus && !search_active"
mode = "i"

[[keymaps]]
key = "alt+]"
command = "inline_completion.next"
when = "inline_completion_visible && !search_focus && !modal_focus && !list_focus && !search_active"
mode = "i"

[[keymaps]]
key = "alt+\\"
command = "inline_completion.invoke"
when = "!inline_completion_visible && !search_focus && !modal_focus && !list_focus && !search_active"
mode = "i"

[[keymaps]]
key = "right"
command = "right"
mode = "inv"

[[keymaps]]
key = "left"
command = "left"
mode = "inv"

[[keymaps]]
key = "up"
command = "up"
when = "!list_focus"
mode = "inv"

[[keymaps]]
key = "down"
command = "down"
when = "!list_focus"
mode = "inv"

[[keymaps]]
key = "ctrl+h"
command = "delete_backward"
mode = "i"

[[keymaps]]
key = "enter"
command = "insert_new_line"
when = "!list_focus && !input_focus"
mode = "i"

[[keymaps]]
key = "tab"
command = "focus_replace_editor"
when = "search_focus"

[[keymaps]]
key = "tab"
command = "focus_find_editor"
when = "replace_focus"

[[keymaps]]
key = "shift+enter"
command = "search_backward"
when = "search_focus"
mode = "i"

[[keymaps]]
key = "enter"
command = "search_forward"
when = "search_focus"
mode = "i"

[[keymaps]]
key = "enter"
command = "global_search_refresh"
when = "global_search_focus"
mode = "i"

[[keymaps]]
key = "enter"
command = "confirm_rename"
when = "rename_focus"
mode = "i"

[[keymaps]]
key = "tab"
command = "insert_tab"
when = "!in_snippet && !completion_focus && !inline_completion_visible && !search_focus && !replace_focus"
mode = "i"

[[keymaps]]
key = "ctrl+m"
command = "insert_new_line"
when = "!list_focus"
mode = "i"

[[keymaps]]
key = "alt+shift+up"
command = "duplicate_line_up"
mode = "i"

[[keymaps]]
key = "alt+shift+down"
command = "duplicate_line_down"
mode = "i"

# ------------------------------------ Modal -----------------------------------------

[[keymaps]]
key = "esc"
command = "normal_mode"
mode = "niv"
when = "!search_focus && !modal_focus && !search_active && !inline_completion_visible"

[[keymaps]]
key = "ctrl+c"
command = "normal_mode"
mode = "niv"
when = "!search_focus && !modal_focus && !search_active"

[[keymaps]]
key = "ctrl+["
command = "normal_mode"
mode = "niv"
when = "!search_focus && !search_active"

[[keymaps]]
key = "shift+;"
command = "palette.command"
mode = "n"

[[keymaps]]
key = "shift+g"
command = "go_to_line_default_last"
mode = "nv"

[[keymaps]]
key = "g g"
command = "go_to_line_default_first"
mode = "nv"

[[keymaps]]
key = "h"
command = "left"
mode = "nv"

[[keymaps]]
key = "j"
command = "down"
mode = "nv"

[[keymaps]]
key = "k"
command = "up"
mode = "nv"

[[keymaps]]
key = "l"
command = "right"
mode = "nv"

[[keymaps]]
key = "a"
command = "append"
mode = "n"

[[keymaps]]
key = "shift+a"
command = "append_end_of_line"
mode = "n"

[[keymaps]]
key = "i"
command = "insert_mode"
mode = "n"

[[keymaps]]
key = "u"
command = "undo"
mode = "n"

[[keymaps]]
key = "ctrl+r"
command = "redo"
mode = "n"

[[keymaps]]
key = "ctrl+i"
command = "jump_location_forward_local"
mode = "n"

[[keymaps]]
key = "ctrl+o"
command = "jump_location_backward_local"
mode = "n"

[[keymaps]]
key = "ctrl+n"
command = "goto_definition"
mode = "n"

[[keymaps]]
key = "g d"
command = "goto_definition"
mode = "n"

[[keymaps]]
key = "g h"
command = "show_hover"
mode = "n"

[[keymaps]]
key = "p"
command = "paste"
mode = "nv"

[[keymaps]]
key = "shift+p"
command = "paste_before"
mode = "nv"

[[keymaps]]
key = "shift+j"
command = "join_lines"
mode = "n"

[[keymaps]]
key = "y"
command = "yank"
mode = "v"

[[keymaps]]
key = "x"
command = "delete_forward"
mode = "nv"

[[keymaps]]
key = "ctrl+p"
command = "get_references"
mode = "n"

[[keymaps]]
key = "s"
command = "delete_forward_and_insert"
mode = "nv"

[[keymaps]]
key = "c"
command = "delete_forward_and_insert"
mode = "v"

[[keymaps]]
key = "shift+s"
command = "delete_line_and_insert"
mode = "n"

[[keymaps]]
key = "c c"
command = "delete_line_and_insert"
mode = "n"

[[keymaps]]
key = "c w"
command = "delete_word_and_insert"
mode = "n"

[[keymaps]]
key = "c e"
command = "delete_word_and_insert"
mode = "n"

[[keymaps]]
key = "shift+i"
command = "insert_first_non_blank"
mode = "nv"

[[keymaps]]
key = "o"
command = "new_line_below"
mode = "nv"

[[keymaps]]
key = "0"
command = "line_start"
mode = "nv"

[[keymaps]]
key = "shift+6"
command = "line_start_non_blank"
mode = "nv"

[[keymaps]]
key = "shift+4"
command = "line_end"
mode = "nv"

[[keymaps]]
key = "w"
command = "word_forward"
mode = "nv"

[[keymaps]]
key = "e"
command = "word_end_forward"
mode = "nv"

[[keymaps]]
key = "b"
command = "word_backward"
mode = "nv"

[[keymaps]]
key = "shift+o"
command = "new_line_above"
mode = "nv"

[[keymaps]]
key = "ctrl+e"
command = "scroll_down"
mode = "nv"

[[keymaps]]
key = "ctrl+y"
command = "scroll_up"
mode = "nv"

[[keymaps]]
key = "ctrl+u"
command = "page_up"
mode = "nv"

[[keymaps]]
key = "ctrl+d"
command = "page_down"
mode = "nv"

[[keymaps]]
key = "z z"
command = "center_of_window"
mode = "nv"

[[keymaps]]
key = "z t"
command = "top_of_window"
mode = "nv"

[[keymaps]]
key = "z b"
command = "bottom_of_window"
mode = "nv"

[[keymaps]]
key = "d"
command = "delete_forward"
mode = "v"

[[keymaps]]
key = "m"
command = "create_mark"
mode = "nv"

[[keymaps]]
key = "'"
command = "go_to_mark"
mode = "nv"

[[keymaps]]
key = "f"
command = "inline_find_right"
mode = "nv"

[[keymaps]]
key = "shift+f"
command = "inline_find_left"
mode = "nv"

[[keymaps]]
key = ";"
command = "repeat_last_inline_find"
mode = "nv"

[[keymaps]]
key = "d"
command = "motion_mode_delete"
mode = "n"

[[keymaps]]
key = "shift+."
command = "motion_mode_indent"
mode = "n"

[[keymaps]]
key = "shift+,"
command = "motion_mode_outdent"
mode = "n"

[[keymaps]]
key = "y"
command = "motion_mode_yank"
mode = "n"

[[keymaps]]
key = "shift+8"
command = "search_whole_word_forward"
mode = "nv"

[[keymaps]]
key = "n"
command = "search_forward"
mode = "nv"

[[keymaps]]
key = "shift+n"
command = "search_backward"
mode = "nv"

[[keymaps]]
key = "shift+5"
command = "match_pairs"
mode = "nv"

[[keymaps]]
key = "] shift+0"
command = "next_unmatched_right_bracket"
mode = "nv"

[[keymaps]]
key = "[ shift+9"
command = "previous_unmatched_left_bracket"
mode = "nv"

[[keymaps]]
key = "] shift+]"
command = "next_unmatched_right_curly_bracket"
mode = "nv"

[[keymaps]]
key = "[ shift+["
command = "previous_unmatched_left_curly_bracket"
mode = "nv"

[[keymaps]]
key = "v"
command = "toggle_visual_mode"
mode = "nv"

[[keymaps]]
key = "shift+v"
command = "toggle_linewise_visual_mode"
mode = "nv"

[[keymaps]]
key = "ctrl+v"
command = "toggle_blockwise_visual_mode"
mode = "nv"

[[keymaps]]
key = "ctrl+w l"
command = "split_right"
mode = "n"

[[keymaps]]
key = "ctrl+w h"
command = "split_left"
mode = "n"

[[keymaps]]
key = "ctrl+w j"
command = "split_down"
mode = "n"

[[keymaps]]
key = "ctrl+w k"
command = "split_up"
mode = "n"

[[keymaps]]
key = "ctrl+w s"
command = "split_horizontal"
mode = "n"

[[keymaps]]
key = "ctrl+w v"
command = "split_vertical"
mode = "n"

[[keymaps]]
key = "ctrl+w c"
command = "split_close"
mode = "n"

[[keymaps]]
key = "ctrl+w x"
command = "split_exchange"
mode = "n"

[[keymaps]]
key = "space"
command = "toggle_code_glance"
mode = "nv"

[[keymaps]]
key = "shift+."
command = "indent_line"
mode = "v"

[[keymaps]]
key = "shift+,"
command = "outdent_line"
mode = "v"

[[keymaps]]
key = "shift+c"
command = "delete_to_end_and_insert"
mode = "n"
