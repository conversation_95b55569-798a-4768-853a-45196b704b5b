#:schema ../extra/schemas/icon-theme.json

[icon-theme]
name = "<PERSON><PERSON><PERSON>"

[icon-theme.ui]
"logo" = "lapce_logo.svg"
"menu" = "menu.svg"
"link" = "link.svg"
"error" = "error.svg"
"add" = "add.svg"
"close" = "close.svg"
"remote" = "remote.svg"
"unsaved" = "circle-filled.svg"
"warning" = "warning.svg"
"problem" = "problem.svg"
"debug" = "debug.svg"
"debug_breakpoint" = "circle-filled.svg"
"debug_alt" = "debug-alt.svg"
"debug_small" = "debug-alt-small.svg"
"debug_restart" = "debug-restart.svg"
"debug_continue" = "debug-continue.svg"
"debug_step_over" = "debug-step-over.svg"
"debug_step_into" = "debug-step-into.svg"
"debug_step_out" = "debug-step-out.svg"
"debug_pause" = "debug-pause.svg"
"debug_stop" = "debug-stop.svg"
"debug_console" = "debug-console.svg"
"debug_disconnect" = "debug-disconnect.svg"
"start" = "debug-start.svg"
"run_errors" = "run-errors.svg"
"settings" = "settings-gear.svg"
"terminal" = "terminal.svg"
"lightbulb" = "lightbulb.svg"
"extensions" = "extensions.svg"
"keyboard" = "keyboard.svg"
"breadcrumb_separator" = "chevron-right.svg"
"symbol_color" = "symbol-color.svg"
"type_hierarchy" = "type-hierarchy.svg"

"window.close" = "chrome-close.svg"
"window.restore" = "chrome-restore.svg"
"window.maximize" = "chrome-maximize.svg"
"window.minimize" = "chrome-minimize.svg"

"file" = "file.svg"
"file_explorer" = "files.svg"
"file_picker_up" = "arrow-up.svg"

"image_loading" = "refresh.svg"
"image_error" = "error.svg"

"scm.icon" = "source-control.svg"
"scm.diff.modified" = "diff-modified.svg"
"scm.diff.added" = "diff-added.svg"
"scm.diff.removed" = "diff-removed.svg"
"scm.diff.renamed" = "diff-renamed.svg"
"scm.change.add" = "add.svg"
"scm.change.remove" = "remove.svg"

"palette.menu" = "chevron-down.svg"

"fold" = "fold.svg"
"fold.up" = "fold-up.svg"
"fold.down" = "fold-down.svg"

"dropdown.arrow" = "chevron-down.svg"

"panel.fold-down" = "chevron-down.svg"
"panel.fold-up" = "chevron-right.svg"

"location.forward" = "arrow-right.svg"
"location.backward" = "arrow-left.svg"

"item.opened" = "chevron-down.svg"
"item.closed" = "chevron-right.svg"

"directory.closed" = "folder.svg"
"directory.opened" = "folder-opened.svg"

"panel.restore" = "chevron-down.svg"
"panel.maximise" = "chevron-right.svg"

"split.horizontal" = "split-horizontal.svg"

"tab.previous" = "chevron-left.svg"
"tab.next" = "chevron-right.svg"

"sidebar.left.on" = "layout-sidebar-left.svg"
"sidebar.left.off" = "layout-sidebar-left-off.svg"
"sidebar.right.on" = "layout-sidebar-right.svg"
"sidebar.right.off" = "layout-sidebar-right-off.svg"

"layout.panel.on" = "layout-panel.svg"
"layout.panel.off" = "layout-panel-off.svg"

"search.icon" = "search.svg"
"search.clear" = "close.svg"
"search.forward" = "arrow-down.svg"
"search.backward" = "arrow-up.svg"
"search.case_sensitive" = "case-sensitive.svg"
"search.whole_word" = "whole-word.svg"
"search.regex" = "regex.svg"
"search.replace" = "replace.svg"
"search.replace_all" = "replace-all.svg"

"document_symbol" = "symbol-class.svg"
"references" = "references.svg"
"implementation" = "combine.svg"
"symbol_kind.array" = "symbol-array.svg"
"symbol_kind.boolean" = "symbol-boolean.svg"
"symbol_kind.class" = "symbol-class.svg"
"symbol_kind.constant" = "symbol-constant.svg"
"symbol_kind.enum_member" = "symbol-enum-member.svg"
"symbol_kind.enum" = "symbol-enum.svg"
"symbol_kind.event" = "symbol-event.svg"
"symbol_kind.field" = "symbol-field.svg"
"symbol_kind.file" = "symbol-file.svg"
"symbol_kind.function" = "symbol-method.svg"
"symbol_kind.interface" = "symbol-interface.svg"
"symbol_kind.key" = "symbol-key.svg"
"symbol_kind.method" = "symbol-method.svg"
"symbol_kind.namespace" = "symbol-namespace.svg"
"symbol_kind.number" = "symbol-numeric.svg"
"symbol_kind.object" = "symbol-namespace.svg"
"symbol_kind.operator" = "symbol-operator.svg"
"symbol_kind.property" = "symbol-property.svg"
"symbol_kind.string" = "symbol-string.svg"
"symbol_kind.struct" = "symbol-structure.svg"
"symbol_kind.type_parameter" = "symbol-parameter.svg"
"symbol_kind.variable" = "symbol-variable.svg"

"completion_item_kind.class" = "symbol-class.svg"
"completion_item_kind.constant" = "symbol-constant.svg"
"completion_item_kind.enum_member" = "symbol-enum-member.svg"
"completion_item_kind.enum" = "symbol-enum.svg"
"completion_item_kind.field" = "symbol-field.svg"
"completion_item_kind.function" = "symbol-method.svg"
"completion_item_kind.interface" = "symbol-interface.svg"
"completion_item_kind.keyword" = "symbol-keyword.svg"
"completion_item_kind.method" = "symbol-method.svg"
"completion_item_kind.module" = "symbol-namespace.svg"
"completion_item_kind.property" = "symbol-property.svg"
"completion_item_kind.snippet" = "symbol-snippet.svg"
"completion_item_kind.string" = "symbol-string.svg"
"completion_item_kind.struct" = "symbol-structure.svg"
"completion_item_kind.variable" = "symbol-variable.svg"

[icon-theme.foldername]

[icon-theme.filename]

[icon-theme.extension]
