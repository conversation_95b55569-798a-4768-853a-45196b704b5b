on:
  push:
    branches:
      - master
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
    paths:
      - Cargo.toml
      - Cargo.lock
      - lapce-**

name: CI

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

env:
  CARGO_TERM_COLOR: always
  CARGO_REGISTRIES_CRATES_IO_PROTOCOL: sparse

jobs:
  build:
    name: Rust on ${{ matrix.os }}
    if: github.event.pull_request.draft == false
    needs: [fmt, clippy]
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest, macos-latest, windows-latest ]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4

      - name: Install dependencies on Ubuntu
        if: startsWith(matrix.os, 'ubuntu')
        run: sudo make ubuntu-deps

      - name: Update toolchain & add llvm-tools
        run: |
          rustup update --no-self-update

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - name: Fetch dependencies
        run: cargo fetch --locked

      - name: Build
        run: cargo build --frozen

      - name: Build as portable
        if: startsWith(matrix.os, 'windows')
        run: cargo build --frozen --features lapce-app/portable

      - name: Free space on Windows
        if: startsWith(matrix.os, 'windows')
        run: cargo clean

      - name: Run doc tests
        run: cargo test --doc --workspace

  fmt:
    name: Rustfmt
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Update toolchain & add rustfmt
        run: |
          rustup update
          rustup component add rustfmt

      - name: Run rustfmt
        run: cargo fmt --all --check

  clippy:
    name: Clippy on ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest, windows-latest, macos-latest ]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4

      - name: Update toolchain & add clippy
        run: |
          rustup update --no-self-update
          rustup component add clippy

      - name: Install dependencies on Ubuntu
        if: startsWith(matrix.os, 'ubuntu')
        run: sudo make ubuntu-deps

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        
      - name: Fetch dependencies
        run: cargo fetch --locked

      - name: Run clippy
        run: cargo clippy
