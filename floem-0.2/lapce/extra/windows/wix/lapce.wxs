<?xml version="1.0" encoding="windows-1252"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi" xmlns:util="http://schemas.microsoft.com/wix/UtilExtension">
    <Product Name="Lapce" Id="*" UpgradeCode="9c09a374-1135-4782-959f-2dec376a1dfa" Language="1033" Codepage="1252" Version="0.4.2" Manufacturer="Lapce">
        <Package InstallerVersion="200" Compressed="yes" InstallScope="perMachine"/>
        <MajorUpgrade AllowSameVersionUpgrades="yes" DowngradeErrorMessage="A newer version of [ProductName] is already installed."/>
        <Icon Id="lapce.exe" SourceFile=".\extra\windows\lapce.ico"/>
        <WixVariable Id="WixUILicenseRtf" Value=".\extra\windows\wix\license.rtf"/>
        <Property Id="ARPPRODUCTICON" Value="lapce.exe"/>
        <MediaTemplate EmbedCab="yes"/>
        <UIRef Id="WixUI_Minimal"/>

        <Feature Id="ProductFeature" Title="ConsoleApp" Level="1">
            <ComponentRef Id="LapceExe"/>
            <ComponentRef Id="LapceShortcut"/>
            <ComponentRef Id="ModifyPathEnv"/>
            <ComponentRef Id="ContextMenu"/>
        </Feature>

        <!-- Create directories -->
        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="ProgramFiles64Folder">
                <Directory Id="LapceProgramFiles" Name="Lapce"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="LapceProgramMenu" Name="Lapce"/>
            </Directory>
        </Directory>

        <!-- Application binaries -->
        <DirectoryRef Id="LapceProgramFiles">
            <Component Id="LapceExe" Guid="*">
                <File Id="LapceExeFile" Source=".\target\release-lto\lapce.exe" Name="lapce.exe" KeyPath="yes"/>
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="LapceProgramMenu">
            <!-- Application shortcut -->
            <Component Id="LapceShortcut" Guid="************************************">
                <Shortcut Id="LapceShortcutFile" Icon="lapce.exe" Name="Lapce" Description="Lightning-fast and Powerful Code Editor" Target="[LapceProgramFiles]lapce.exe"/>
                <RemoveFolder Id="LapceProgramMenu" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\Microsoft\Lapce" Name="installed" Type="integer" Value="1" KeyPath="yes"/>
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="LapceProgramFiles">
            <!-- Add to PATH -->
            <Component Id="ModifyPathEnv" Guid="0581ccb0-5db8-4935-8b07-8f46bc2d7171" KeyPath="yes">
                <Environment Id="PathEnv" Value="[LapceProgramFiles]" Name="PATH" Permanent="no" Part="first" Action="set" System="yes"/>
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="TARGETDIR">
            <!-- Add context menu -->
            <Component Id="ContextMenu" Guid="22ad399b-1d77-416c-ae33-57e8f8511177">
                <RegistryKey Root="HKCU" Key="Software\Classes\Directory\Background\shell\Open Lapce here\command">
                    <RegistryValue Type="string" Value="[LapceProgramFiles]lapce.exe &quot;%V&quot;" KeyPath="yes"/>
                </RegistryKey>
                <RegistryKey Root="HKCU" Key="Software\Classes\Directory\Background\shell\Open Lapce here">
                    <RegistryValue Type="string" Name="Icon" Value="[LapceProgramFiles]lapce.exe"/>
                </RegistryKey>
            </Component>
        </DirectoryRef>
    </Product>
</Wix>
