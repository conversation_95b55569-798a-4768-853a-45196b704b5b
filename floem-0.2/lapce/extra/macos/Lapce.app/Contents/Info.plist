<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>CFBundleDevelopmentRegion</key>
  <string>en</string>
  <key>CFBundleExecutable</key>
  <string>lapce</string>
  <key>CFBundleIdentifier</key>
  <string>io.lapce</string>
  <key>CFBundleInfoDictionaryVersion</key>
  <string>6.0</string>
  <key>CFBundleName</key>
  <string>Lapce</string>
  <key>CFBundlePackageType</key>
  <string>APPL</string>
  <key>CFBundleShortVersionString</key>
  <string>0.4.2</string>
  <key>CFBundleSupportedPlatforms</key>
  <array>
    <string>MacOSX</string>
  </array>
  <key>CFBundleVersion</key>
  <string>1</string>
  <key>CFBundleIconFile</key>
  <string>lapce.icns</string>
  <key>NSHighResolutionCapable</key>
  <true/>
  <key>NSMainNibFile</key>
  <string></string>
  <key>NSSupportsAutomaticGraphicsSwitching</key>
  <true/>
  <key>CFBundleDisplayName</key>
  <string>Lapce</string>
  <key>NSRequiresAquaSystemAppearance</key>
  <string>NO</string>
    <key>NSAppleEventsUsageDescription</key>
    <string>An application in Lapce would like to access AppleScript.</string>
    <key>NSCalendarsUsageDescription</key>
    <string>An application in Lapce would like to access calendar data.</string>
    <key>NSCameraUsageDescription</key>
    <string>An application in Lapce would like to access the camera.</string>
    <key>NSContactsUsageDescription</key>
    <string>An application in Lapce wants to access your contacts.</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>An application in Lapce would like to access your location information, even in the background.</string>
    <key>NSLocationUsageDescription</key>
    <string>An application in Lapce would like to access your location information.</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>An application in Lapce would like to access your location information while active.</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>An application in Lapce would like to access your microphone.</string>
    <key>NSRemindersUsageDescription</key>
    <string>An application in Lapce would like to access your reminders.</string>
    <key>NSSystemAdministrationUsageDescription</key>
    <string>An application in Lapce requires elevated permissions.</string>
</dict>
</plist>